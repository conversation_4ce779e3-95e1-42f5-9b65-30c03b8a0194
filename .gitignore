# Build directories
build/
Build/
builds/
Builds/
cmake-build-*/

# IDE files
.vscode/
.vs/
*.vcxproj*
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates
*.xcodeproj/
*.xcworkspace/

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/

# JUCE
JuceLibraryCode/
*.jucer.bak

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Audio files (temporary)
*.wav
*.aiff
*.mp3
*.flac
*.ogg
temp_audio/

# Project files
*.dawdan
projects/temp/

# Logs
*.log
logs/

# Package managers
conan.lock
vcpkg_installed/

# Backup files
*.bak
*.backup
*~
