#include "MidiEngine.h"

using namespace juce;

//==============================================================================
MidiEngine::MidiEngine()
{
    refreshMidiDevices();
    detectLaunchpad();
}

MidiEngine::~MidiEngine()
{
    closeMidiInput();
    closeMidiOutput();
}

//==============================================================================
void MidiEngine::refreshMidiDevices()
{
    // Refresh the list of available MIDI devices
    auto inputDevices = MidiInput::getAvailableDevices();
    auto outputDevices = MidiOutput::getAvailableDevices();
    
    DBG("Found " + String(inputDevices.size()) + " MIDI input devices");
    DBG("Found " + String(outputDevices.size()) + " MIDI output devices");
    
    for (auto& device : inputDevices)
        DBG("MIDI Input: " + device.name);
        
    for (auto& device : outputDevices)
        DBG("MIDI Output: " + device.name);
}

StringArray MidiEngine::getAvailableInputDevices() const
{
    StringArray deviceNames;
    auto devices = MidiInput::getAvailableDevices();
    
    for (auto& device : devices)
        deviceNames.add(device.name);
        
    return deviceNames;
}

StringArray MidiEngine::getAvailableOutputDevices() const
{
    StringArray deviceNames;
    auto devices = MidiOutput::getAvailableDevices();
    
    for (auto& device : devices)
        deviceNames.add(device.name);
        
    return deviceNames;
}

//==============================================================================
bool MidiEngine::openMidiInput(const String& deviceName)
{
    closeMidiInput();
    
    auto devices = MidiInput::getAvailableDevices();
    
    for (auto& device : devices)
    {
        if (device.name == deviceName)
        {
            midiInput = MidiInput::openDevice(device.identifier, this);
            if (midiInput)
            {
                midiInput->start();
                DBG("Opened MIDI input: " + deviceName);
                return true;
            }
        }
    }
    
    DBG("Failed to open MIDI input: " + deviceName);
    return false;
}

bool MidiEngine::openMidiOutput(const String& deviceName)
{
    closeMidiOutput();
    
    auto devices = MidiOutput::getAvailableDevices();
    
    for (auto& device : devices)
    {
        if (device.name == deviceName)
        {
            midiOutput = MidiOutput::openDevice(device.identifier);
            if (midiOutput)
            {
                DBG("Opened MIDI output: " + deviceName);
                return true;
            }
        }
    }
    
    DBG("Failed to open MIDI output: " + deviceName);
    return false;
}

void MidiEngine::closeMidiInput()
{
    if (midiInput)
    {
        midiInput->stop();
        midiInput.reset();
        DBG("Closed MIDI input");
    }
}

void MidiEngine::closeMidiOutput()
{
    if (midiOutput)
    {
        midiOutput.reset();
        DBG("Closed MIDI output");
    }
}

//==============================================================================
bool MidiEngine::detectLaunchpad()
{
    launchpadDetected = false;
    launchpadModel = "";
    launchpadType = 0;
    
    // Check for different Launchpad models
    if (detectLaunchpadPro())
    {
        launchpadModel = "Launchpad Pro";
        launchpadType = 2;
        launchpadDetected = true;
        DBG("Detected: " + launchpadModel);
        return true;
    }
    
    if (detectLaunchpadX())
    {
        launchpadModel = "Launchpad X";
        launchpadType = 3;
        launchpadDetected = true;
        DBG("Detected: " + launchpadModel);
        return true;
    }
    
    if (detectLaunchpadMini())
    {
        launchpadModel = "Launchpad Mini";
        launchpadType = 1;
        launchpadDetected = true;
        DBG("Detected: " + launchpadModel);
        return true;
    }
    
    DBG("No Launchpad detected");
    return false;
}

bool MidiEngine::detectLaunchpadMini()
{
    auto devices = MidiInput::getAvailableDevices();
    
    for (auto& device : devices)
    {
        if (device.name.containsIgnoreCase("Launchpad Mini") ||
            device.name.containsIgnoreCase("Launchpad S"))
        {
            return openMidiInput(device.name) && openMidiOutput(device.name);
        }
    }
    
    return false;
}

bool MidiEngine::detectLaunchpadPro()
{
    auto devices = MidiInput::getAvailableDevices();
    
    for (auto& device : devices)
    {
        if (device.name.containsIgnoreCase("Launchpad Pro"))
        {
            return openMidiInput(device.name) && openMidiOutput(device.name);
        }
    }
    
    return false;
}

bool MidiEngine::detectLaunchpadX()
{
    auto devices = MidiInput::getAvailableDevices();
    
    for (auto& device : devices)
    {
        if (device.name.containsIgnoreCase("Launchpad X"))
        {
            return openMidiInput(device.name) && openMidiOutput(device.name);
        }
    }
    
    return false;
}

//==============================================================================
void MidiEngine::handleIncomingMidiMessage(MidiInput* source, const MidiMessage& message)
{
    if (launchpadDetected)
    {
        processLaunchpadMessage(message);
    }
    
    // Handle general MIDI messages
    if (message.isNoteOn())
    {
        if (onNoteOn)
            onNoteOn(message.getNoteNumber(), message.getFloatVelocity());
    }
    else if (message.isNoteOff())
    {
        if (onNoteOff)
            onNoteOff(message.getNoteNumber());
    }
    else if (message.isController())
    {
        if (onControlChange)
            onControlChange(message.getControllerNumber(), message.getControllerValue() / 127.0f);
    }
}

void MidiEngine::processLaunchpadMessage(const MidiMessage& message)
{
    if (message.isNoteOn() || message.isNoteOff())
    {
        int note = message.getNoteNumber();
        float velocity = message.getFloatVelocity();
        
        // Convert note to grid coordinates
        if (launchpadType == 1) // Mini
        {
            if (note >= 0x00 && note <= 0x7F)
            {
                int x = note % 16;
                int y = note / 16;
                
                if (x < 8 && y < 8) // Valid grid position
                {
                    if (message.isNoteOn() && velocity > 0)
                    {
                        if (onPadPressed)
                            onPadPressed(x, y, velocity);
                    }
                    else
                    {
                        if (onPadReleased)
                            onPadReleased(x, y);
                    }
                }
            }
        }
        else if (launchpadType == 2 || launchpadType == 3) // Pro or X
        {
            // Pro/X use different note mapping
            if (note >= 0x51 && note <= 0x58) // Grid notes
            {
                int x = (note - 0x51) % 8;
                int y = (note - 0x51) / 8;
                
                if (message.isNoteOn() && velocity > 0)
                {
                    if (onPadPressed)
                        onPadPressed(x, y, velocity);
                }
                else
                {
                    if (onPadReleased)
                        onPadReleased(x, y);
                }
            }
        }
    }
}

void MidiEngine::sendMidiMessage(const MidiMessage& message)
{
    if (midiOutput)
    {
        midiOutput->sendMessageNow(message);
    }
}

//==============================================================================
void MidiEngine::setLaunchpadLED(int x, int y, Colour colour)
{
    if (!launchpadDetected || !midiOutput)
        return;
        
    int note = 0;
    
    if (launchpadType == 1) // Mini
    {
        note = y * 16 + x;
    }
    else if (launchpadType == 2 || launchpadType == 3) // Pro or X
    {
        note = 0x51 + y * 8 + x;
    }
    
    int velocity = colourToLaunchpadVelocity(colour);
    auto message = MidiMessage::noteOn(1, note, (uint8)velocity);
    sendMidiMessage(message);
}

void MidiEngine::setLaunchpadLED(int padIndex, Colour colour)
{
    int x = padIndex % 8;
    int y = padIndex / 8;
    setLaunchpadLED(x, y, colour);
}

void MidiEngine::clearAllLaunchpadLEDs()
{
    if (!launchpadDetected)
        return;
        
    for (int y = 0; y < 8; ++y)
    {
        for (int x = 0; x < 8; ++x)
        {
            setLaunchpadLED(x, y, Colours::black);
        }
    }
}

void MidiEngine::setLaunchpadMode(int mode)
{
    if (!launchpadDetected || !midiOutput)
        return;
        
    // Send mode change SysEx (implementation depends on Launchpad model)
    if (launchpadType == 2 || launchpadType == 3) // Pro or X
    {
        Array<uint8> sysexData = { 0xF0, 0x00, 0x20, 0x29, 0x02, 0x0C, 0x00, (uint8)mode, 0xF7 };
        sendLaunchpadSysEx(sysexData);
    }
}

//==============================================================================
void MidiEngine::sendLaunchpadSysEx(const Array<uint8>& data)
{
    if (midiOutput)
    {
        auto message = MidiMessage::createSysExMessage(data.getRawDataPointer(), data.size());
        sendMidiMessage(message);
    }
}

int MidiEngine::colourToLaunchpadVelocity(Colour colour)
{
    if (colour == Colours::black) return 0;
    if (colour == Colours::red) return 5;
    if (colour == Colours::green) return 17;
    if (colour == Colours::yellow) return 13;
    if (colour == Colours::orange) return 9;
    if (colour == Colours::blue) return 45;
    if (colour == Colours::purple) return 53;
    if (colour == Colours::white) return 3;
    
    // Default to dim red for unknown colors
    return 1;
}
