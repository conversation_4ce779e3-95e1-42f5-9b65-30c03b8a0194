^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\DAW DAN\BUILD_VS\CMAKEFILES\F266866CA426A03259C8744F2CF8AD95\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SC:/Users/<USER>/Documents/augment-projects/DAW Dan" "-BC:/Users/<USER>/Documents/augment-projects/DAW Dan/build_vs" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "C:/Users/<USER>/Documents/augment-projects/DAW Dan/build_vs/DAWDanSimple.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
