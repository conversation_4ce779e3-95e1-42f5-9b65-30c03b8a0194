@echo off
echo Building DAW Dan GUI with Visual Studio...

REM Add CMake to PATH
set "PATH=C:\Program Files\CMake\bin;%PATH%"

REM Find Visual Studio installation
set "VS_PATH="
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019"
    echo Found Visual Studio 2019
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022"
    echo Found Visual Studio 2022
) else if exist "C:\Program Files\Microsoft Visual Studio\2022" (
    set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022"
    echo Found Visual Studio 2022
) else (
    echo Visual Studio not found, trying MSBuild...
    set "PATH=C:\Program Files (x86)\MSBuild\Current\Bin;%PATH%"
)

REM Clean previous build
if exist build_gui rmdir /s /q build_gui
mkdir build_gui

REM Copy CMake file
copy CMakeLists_GUI.txt CMakeLists.txt

cd build_gui

REM Test CMake
echo Testing CMake...
cmake --version
if %errorlevel% neq 0 (
    echo CMake not found!
    pause
    exit /b 1
)

REM Configure with Visual Studio
echo Configuring with Visual Studio...
cmake -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Release ..

if %errorlevel% neq 0 (
    echo Cleaning cache and trying Visual Studio 16 2019...
    del CMakeCache.txt 2>nul
    rmdir /s /q CMakeFiles 2>nul
    cmake -G "Visual Studio 16 2019" -A x64 -DCMAKE_BUILD_TYPE=Release ..
    
    if %errorlevel% neq 0 (
        echo Cleaning cache and trying NMake...
        del CMakeCache.txt 2>nul
        rmdir /s /q CMakeFiles 2>nul
        cmake -G "NMake Makefiles" -DCMAKE_BUILD_TYPE=Release ..
        
        if %errorlevel% neq 0 (
            echo CMake configuration failed!
            pause
            exit /b 1
        )
    )
)

REM Build
echo Building...
cmake --build . --config Release

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build complete! DAWDanGUI.exe is ready.
echo.
echo To run: .\Release\DAWDanGUI.exe
echo.
pause
