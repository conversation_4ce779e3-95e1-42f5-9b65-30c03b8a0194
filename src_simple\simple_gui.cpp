// Simple GUI wrapper for DAW Dan
// This creates a minimal Windows GUI that launches the console version
#include <windows.h>
#include <string>

const wchar_t* CLASS_NAME = L"DAWDanGUI";
const wchar_t* WINDOW_TITLE = L"DAW Dan - Live Looping Studio";

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    // Register window class
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = CLASS_NAME;
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);

    RegisterClass(&wc);

    // Create window
    HWND hwnd = CreateWindowEx(
        0,
        CLASS_NAME,
        WINDOW_TITLE,
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 800, 600,
        nullptr, nullptr, hInstance, nullptr
    );

    if (hwnd == nullptr) {
        return 0;
    }

    ShowWindow(hwnd, nCmdShow);

    // Message loop
    MSG msg = {};
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return 0;
}

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg) {
    case WM_CREATE:
        {
            // Create buttons
            CreateWindow(L"BUTTON", L"Launch DAW Dan Console",
                WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_DEFPUSHBUTTON,
                50, 50, 200, 50,
                hwnd, (HMENU)1, GetModuleHandle(nullptr), nullptr);

            CreateWindow(L"STATIC", 
                L"DAW Dan - Live Looping Studio\n\n"
                L"Click the button below to launch the console version.\n\n"
                L"Console Commands:\n"
                L"• p - Play/Stop\n"
                L"• r - Record/Stop Recording\n"
                L"• 1-9, 0 - Trigger loops\n"
                L"• s - Show status\n"
                L"• q - Quit",
                WS_VISIBLE | WS_CHILD,
                50, 120, 700, 400,
                hwnd, nullptr, GetModuleHandle(nullptr), nullptr);
        }
        return 0;

    case WM_COMMAND:
        if (LOWORD(wParam) == 1) {
            // Launch console version
            STARTUPINFO si = {};
            PROCESS_INFORMATION pi = {};
            si.cb = sizeof(si);

            std::wstring cmdLine = L".\\build_vs\\Release\\DAWDanSimple.exe";
            
            if (CreateProcess(nullptr, &cmdLine[0], nullptr, nullptr, FALSE, 
                            CREATE_NEW_CONSOLE, nullptr, nullptr, &si, &pi)) {
                CloseHandle(pi.hProcess);
                CloseHandle(pi.hThread);
                MessageBox(hwnd, L"DAW Dan Console launched successfully!\nCheck the console window.", 
                          L"Success", MB_OK | MB_ICONINFORMATION);
            } else {
                MessageBox(hwnd, L"Failed to launch DAW Dan Console.\nMake sure DAWDanSimple.exe exists in build_vs\\Release\\", 
                          L"Error", MB_OK | MB_ICONERROR);
            }
        }
        return 0;

    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            
            // Set background color
            RECT rect;
            GetClientRect(hwnd, &rect);
            HBRUSH brush = CreateSolidBrush(RGB(26, 26, 26));
            FillRect(hdc, &rect, brush);
            DeleteObject(brush);
            
            EndPaint(hwnd, &ps);
        }
        return 0;

    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}
