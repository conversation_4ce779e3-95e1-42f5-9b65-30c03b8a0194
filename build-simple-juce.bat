@echo off
title DAW Dan - Simple JUCE Build
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    DAW DAN - SIMPLE JUCE BUILD              ║
echo  ║                   Step by Step Approach                     ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Set up MinGW environment
echo 🔧 Setting up MinGW environment...
set PATH=C:\msys64\mingw64\bin;C:\msys64\usr\bin;%PATH%
set CMAKE_MAKE_PROGRAM=C:\msys64\mingw64\bin\mingw32-make.exe
set CMAKE_CXX_COMPILER=C:\msys64\mingw64\bin\g++.exe
set CMAKE_C_COMPILER=C:\msys64\mingw64\bin\gcc.exe

echo ✅ Environment set up
echo CMAKE_MAKE_PROGRAM: %CMAKE_MAKE_PROGRAM%
echo CMAKE_CXX_COMPILER: %CMAKE_CXX_COMPILER%

REM Clean and create build directory
echo.
echo 🧹 Cleaning build directory...
if exist build_simple_juce rmdir /s /q build_simple_juce
mkdir build_simple_juce
cd build_simple_juce

echo.
echo ⚙️ Configuring CMake...
cmake -G "MinGW Makefiles" ^
      -DCMAKE_BUILD_TYPE=Release ^
      -DCMA<PERSON>_MAKE_PROGRAM=%CMAKE_MAKE_PROGRAM% ^
      -DCMAKE_CXX_COMPILER=%CMAKE_CXX_COMPILER% ^
      -DCMAKE_C_COMPILER=%CMAKE_C_COMPILER% ^
      ..

if %errorlevel% neq 0 (
    echo ❌ CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo 🔨 Building project...
%CMAKE_MAKE_PROGRAM% -j2

if %errorlevel% neq 0 (
    echo ❌ Build failed! Trying single-threaded...
    %CMAKE_MAKE_PROGRAM%
    
    if %errorlevel% neq 0 (
        echo ❌ Build failed completely!
        pause
        exit /b 1
    )
)

echo.
echo ✅ Build completed!

REM Look for the executable
echo 🔍 Looking for DAWDan.exe...
if exist "bin\DAWDan.exe" (
    echo ✅ Found: bin\DAWDan.exe
    echo.
    echo 🚀 Would you like to run DAW Dan? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        start "DAW Dan" "bin\DAWDan.exe"
    )
) else (
    echo ⚠️ Executable not found in bin\
    echo Searching entire build directory...
    dir /s *.exe
)

echo.
pause
