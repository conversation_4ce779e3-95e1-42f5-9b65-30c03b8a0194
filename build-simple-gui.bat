@echo off
echo Building DAW Dan Simple GUI Launcher...

REM Add CMake to PATH
set "PATH=C:\Program Files\CMake\bin;%PATH%"

REM Clean previous build
if exist build_simple_gui rmdir /s /q build_simple_gui
mkdir build_simple_gui

REM Copy CMake file
copy CMakeLists_SimpleGUI.txt CMakeLists.txt

cd build_simple_gui

REM Configure with NMake (simpler, less disk space)
echo Configuring with NMake...
cmake -G "NMake Makefiles" -DCMAKE_BUILD_TYPE=Release ..

if %errorlevel% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build
echo Building...
nmake

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build complete! DAWDanSimpleGUI.exe is ready.
echo.
echo This GUI launcher will start the console version.
echo Make sure you have built the console version first with build-vs.bat
echo.
pause
