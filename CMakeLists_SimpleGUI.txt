cmake_minimum_required(VERSION 3.16)

# Project configuration
project(DAWDanSimpleGUI VERSION 1.0.0 LANGUAGES CXX)

# C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Windows-specific settings
if(WIN32)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-DNOMINMAX)
endif()

# Create simple GUI executable (just a launcher)
add_executable(DAWDanSimpleGUI
    src_simple/simple_gui.cpp
)

# Link basic Windows libraries
target_link_libraries(DAWDanSimpleGUI
    user32
    gdi32
    kernel32
)

# Link options for Windows GUI application
target_link_options(DAWDanSimpleGUI PRIVATE
    -mwindows
)

# Define UNICODE for Windows
target_compile_definitions(DAWDanSimpleGUI PRIVATE
    UNICODE
    _UNICODE
)

# Set output directory
set_target_properties(DAWDanSimpleGUI PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/Release
)
