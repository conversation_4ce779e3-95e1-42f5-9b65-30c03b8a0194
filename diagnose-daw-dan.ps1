# DAW Dan Diagnostic Script
Write-Host "=== DAW Dan Diagnostic ===" -ForegroundColor Cyan

# Check if executable exists
$exePath = "build_ps\bin\DAW Dan.exe"
if (Test-Path $exePath) {
    Write-Host "✓ DAW Dan.exe found" -ForegroundColor Green
    $fileInfo = Get-Item $exePath
    Write-Host "  Size: $($fileInfo.Length) bytes" -ForegroundColor Yellow
    Write-Host "  Modified: $($fileInfo.LastWriteTime)" -ForegroundColor Yellow
} else {
    Write-Host "✗ DAW Dan.exe not found!" -ForegroundColor Red
    exit 1
}

# Check DLLs
Write-Host "`nChecking DLLs..." -ForegroundColor Cyan
$dlls = @("libgcc_s_seh-1.dll", "libstdc++-6.dll", "libwinpthread-1.dll")
foreach ($dll in $dlls) {
    $dllPath = "build_ps\bin\$dll"
    if (Test-Path $dllPath) {
        Write-Host "✓ $dll found" -ForegroundColor Green
    } else {
        Write-Host "✗ $dll missing" -ForegroundColor Red
    }
}

# Check system dependencies
Write-Host "`nChecking system..." -ForegroundColor Cyan
Write-Host "OS: $([System.Environment]::OSVersion.VersionString)" -ForegroundColor Yellow
Write-Host "Architecture: $([System.Environment]::Is64BitOperatingSystem)" -ForegroundColor Yellow

# Try to get dependency information
Write-Host "`nTrying to run with error capture..." -ForegroundColor Cyan
try {
    Set-Location "build_ps\bin"
    $process = Start-Process -FilePath ".\DAW Dan.exe" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    
    if ($process.HasExited) {
        Write-Host "✗ Process exited immediately with code: $($process.ExitCode)" -ForegroundColor Red
    } else {
        Write-Host "✓ Process started successfully!" -ForegroundColor Green
        Write-Host "Process ID: $($process.Id)" -ForegroundColor Yellow
        Write-Host "Waiting for process..." -ForegroundColor Yellow
        $process.WaitForExit(5000)  # Wait 5 seconds
        if (!$process.HasExited) {
            Write-Host "✓ DAW Dan is running!" -ForegroundColor Green
            $process.Kill()
        }
    }
} catch {
    Write-Host "✗ Error starting process: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Diagnostic Complete ===" -ForegroundColor Cyan
Write-Host "If DAW Dan won't start, try:" -ForegroundColor Yellow
Write-Host "1. Install Visual C++ Redistributable 2019+" -ForegroundColor White
Write-Host "2. Update Windows audio drivers" -ForegroundColor White
Write-Host "3. Run as Administrator" -ForegroundColor White

Read-Host "Press Enter to continue"
