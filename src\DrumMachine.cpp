#include "DrumMachine.h"

using namespace juce;

//==============================================================================
// DrumSampleVoice Implementation
bool DrumSampleVoice::canPlaySound(SynthesiserSound* sound)
{
    return dynamic_cast<DrumSampleSound*>(sound) != nullptr;
}

void DrumSampleVoice::startNote(int midiNoteNumber, float velocity, SynthesiserSound* sound, int)
{
    if (auto* drumSound = dynamic_cast<DrumSampleSound*>(sound))
    {
        currentSample = drumSound->getAudioData();
        samplePosition = 0;
        level = velocity;
        isPlaying = true;
    }
}

void DrumSampleVoice::stopNote(float, bool)
{
    // Drum samples play to completion, don't stop early
}

void DrumSampleVoice::renderNextBlock(AudioSampleBuffer& outputBuffer, int startSample, int numSamples)
{
    if (!isPlaying || !currentSample)
        return;

    while (--numSamples >= 0 && samplePosition < currentSample->getNumSamples())
    {
        for (int channel = 0; channel < outputBuffer.getNumChannels(); ++channel)
        {
            if (channel < currentSample->getNumChannels())
            {
                float sample = currentSample->getSample(channel, samplePosition) * level;
                outputBuffer.addSample(channel, startSample, sample);
            }
        }
        
        ++samplePosition;
        ++startSample;
    }

    if (samplePosition >= currentSample->getNumSamples())
    {
        isPlaying = false;
        clearCurrentNote();
    }
}

//==============================================================================
// DrumSampleSound Implementation
DrumSampleSound::DrumSampleSound(const String& name, AudioSampleBuffer& source, int midiNote)
    : name_(name), midiNoteNumber_(midiNote)
{
    data.makeCopyOf(source);
}

//==============================================================================
// DrumMachine Implementation
DrumMachine::DrumMachine()
{
    // Create drum pads
    for (int i = 0; i < numPads; ++i)
    {
        drumPads[i] = std::make_unique<TextButton>(padNames[i]);
        drumPads[i]->addListener(this);
        drumPads[i]->setColour(TextButton::buttonColourId, Colour(0xff404040));
        addAndMakeVisible(*drumPads[i]);
    }

    // Add voices to the drum synthesiser
    for (int i = 0; i < 16; ++i)
        drumSynth.addVoice(new DrumSampleVoice());

    // Create drum samples
    createDrumSamples();
}

DrumMachine::~DrumMachine() = default;

//==============================================================================
void DrumMachine::paint(Graphics& g)
{
    // Dark background
    g.fillAll(Colour(0xff2a2a2a));

    // Title
    g.setColour(Colours::white);
    g.setFont(Font(20.0f, Font::bold));
    g.drawText("Drum Machine", getLocalBounds().removeFromTop(40), Justification::centred, true);
}

void DrumMachine::resized()
{
    auto area = getLocalBounds();
    
    // Leave space for title
    area.removeFromTop(40);
    
    // Arrange pads in 4x4 grid
    int padSize = jmin(area.getWidth() / 4, area.getHeight() / 4) - 4;
    
    for (int row = 0; row < 4; ++row)
    {
        for (int col = 0; col < 4; ++col)
        {
            int index = row * 4 + col;
            if (index < numPads)
            {
                int x = area.getX() + col * (padSize + 4);
                int y = area.getY() + row * (padSize + 4);
                drumPads[index]->setBounds(x, y, padSize, padSize);
            }
        }
    }
}

void DrumMachine::buttonClicked(Button* button)
{
    // Find which pad was clicked
    for (int i = 0; i < numPads; ++i)
    {
        if (drumPads[i].get() == button)
        {
            triggerPad(i);
            break;
        }
    }
}

//==============================================================================
void DrumMachine::prepareToPlay(double sampleRate, int blockSize)
{
    currentSampleRate = sampleRate;
    drumSynth.setCurrentPlaybackSampleRate(sampleRate);
    
    DBG("DrumMachine prepared: " << sampleRate << "Hz, " << blockSize << " samples");
}

void DrumMachine::getNextAudioBlock(AudioSampleBuffer& buffer)
{
    // Clear the buffer
    buffer.clear();
    
    // Create a MIDI buffer (empty for now)
    MidiBuffer midiMessages;
    
    // Render the drum synthesiser
    drumSynth.renderNextBlock(buffer, midiMessages, 0, buffer.getNumSamples());
}

void DrumMachine::releaseResources()
{
    DBG("DrumMachine resources released");
}

//==============================================================================
void DrumMachine::processMidiMessage(const MidiMessage& message)
{
    if (message.isNoteOn())
    {
        int note = message.getNoteNumber();
        if (note >= 36 && note < 36 + numPads) // Standard drum mapping starts at C2 (36)
        {
            triggerPad(note - 36, message.getFloatVelocity());
        }
    }
}

void DrumMachine::triggerPad(int padIndex, float velocity)
{
    if (padIndex >= 0 && padIndex < numPads)
    {
        // Trigger the corresponding MIDI note
        int midiNote = 36 + padIndex; // C2 + pad index
        drumSynth.noteOn(1, midiNote, velocity);
        
        // Visual feedback
        drumPads[padIndex]->setColour(TextButton::buttonColourId, Colours::orange);
        Timer::callAfterDelay(100, [this, padIndex]() {
            drumPads[padIndex]->setColour(TextButton::buttonColourId, Colour(0xff404040));
        });
        
        DBG("Drum pad " << padIndex << " (" << padNames[padIndex] << ") triggered");
    }
}

//==============================================================================
void DrumMachine::createDrumSamples()
{
    // Generate basic drum samples
    auto kickSample = generateKickSample(currentSampleRate);
    auto snareSample = generateSnareSample(currentSampleRate);
    auto hihatSample = generateHiHatSample(currentSampleRate);
    auto clapSample = generateClapSample(currentSampleRate);

    // Add samples to synthesiser
    drumSynth.addSound(new DrumSampleSound("Kick", kickSample, 36));
    drumSynth.addSound(new DrumSampleSound("Snare", snareSample, 37));
    drumSynth.addSound(new DrumSampleSound("HiHat", hihatSample, 38));
    drumSynth.addSound(new DrumSampleSound("Clap", clapSample, 39));
    
    // Use variations for other pads
    for (int i = 4; i < numPads; ++i)
    {
        drumSynth.addSound(new DrumSampleSound(padNames[i], hihatSample, 36 + i));
    }
}

AudioSampleBuffer DrumMachine::generateKickSample(double sampleRate)
{
    int length = (int)(sampleRate * 0.5); // 0.5 second kick
    AudioSampleBuffer buffer(2, length);
    
    for (int sample = 0; sample < length; ++sample)
    {
        float t = sample / (float)length;
        float envelope = std::exp(-t * 8.0f); // Exponential decay
        float frequency = 60.0f * (1.0f - t * 0.8f); // Pitch sweep down
        float phase = 2.0f * MathConstants<float>::pi * frequency * sample / (float)sampleRate;
        float value = std::sin(phase) * envelope * 0.8f;
        
        buffer.setSample(0, sample, value);
        buffer.setSample(1, sample, value);
    }
    
    return buffer;
}

AudioSampleBuffer DrumMachine::generateSnareSample(double sampleRate)
{
    int length = (int)(sampleRate * 0.2); // 0.2 second snare
    AudioSampleBuffer buffer(2, length);
    Random random;
    
    for (int sample = 0; sample < length; ++sample)
    {
        float t = sample / (float)length;
        float envelope = std::exp(-t * 15.0f); // Fast decay
        float tone = std::sin(2.0f * MathConstants<float>::pi * 200.0f * sample / (float)sampleRate);
        float noise = random.nextFloat() * 2.0f - 1.0f;
        float value = (tone * 0.3f + noise * 0.7f) * envelope * 0.6f;
        
        buffer.setSample(0, sample, value);
        buffer.setSample(1, sample, value);
    }
    
    return buffer;
}

AudioSampleBuffer DrumMachine::generateHiHatSample(double sampleRate)
{
    int length = (int)(sampleRate * 0.1); // 0.1 second hihat
    AudioSampleBuffer buffer(2, length);
    Random random;
    
    for (int sample = 0; sample < length; ++sample)
    {
        float t = sample / (float)length;
        float envelope = std::exp(-t * 25.0f); // Very fast decay
        float value = (random.nextFloat() * 2.0f - 1.0f) * envelope * 0.4f;
        
        buffer.setSample(0, sample, value);
        buffer.setSample(1, sample, value);
    }
    
    return buffer;
}

AudioSampleBuffer DrumMachine::generateClapSample(double sampleRate)
{
    int length = (int)(sampleRate * 0.15); // 0.15 second clap
    AudioSampleBuffer buffer(2, length);
    Random random;
    
    for (int sample = 0; sample < length; ++sample)
    {
        float t = sample / (float)length;
        float envelope = std::exp(-t * 12.0f);
        float value = (random.nextFloat() * 2.0f - 1.0f) * envelope * 0.5f;
        
        buffer.setSample(0, sample, value);
        buffer.setSample(1, sample, value);
    }
    
    return buffer;
}
