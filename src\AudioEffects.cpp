#include "AudioEffects.h"

using namespace juce;

//==============================================================================
// SimpleReverb Implementation
void SimpleReverb::prepare(double sampleRate, int blockSize)
{
    reverb.setSampleRate(sampleRate);
    reverb.setParameters({0.5f, 0.5f, 0.3f, 0.4f, 0.0f, 0.0f});
    wetBuffer.setSize(2, blockSize);
}

void SimpleReverb::process(AudioBuffer<float>& buffer)
{
    wetBuffer.makeCopyOf(buffer);
    
    if (buffer.getNumChannels() == 1)
    {
        reverb.processMono(wetBuffer.getWritePointer(0), wetBuffer.getNumSamples());
    }
    else if (buffer.getNumChannels() >= 2)
    {
        reverb.processStereo(wetBuffer.getWritePointer(0), wetBuffer.getWritePointer(1), wetBuffer.getNumSamples());
    }
    
    // Mix wet and dry signals
    for (int channel = 0; channel < buffer.getNumChannels(); ++channel)
    {
        auto* dry = buffer.getWritePointer(channel);
        auto* wet = wetBuffer.getReadPointer(jmin(channel, wetBuffer.getNumChannels() - 1));
        
        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            dry[sample] = dry[sample] * (1.0f - wetLevel) + wet[sample] * wetLevel;
        }
    }
}

//==============================================================================
// SimpleDelay Implementation
void SimpleDelay::prepare(double sampleRate, int blockSize)
{
    currentSampleRate = sampleRate;
    delayBufferSize = (int)(sampleRate * 2.0); // 2 second max delay
    delayBuffer.setSize(2, delayBufferSize);
    delayBuffer.clear();
    writePosition = 0;
}

void SimpleDelay::process(AudioBuffer<float>& buffer)
{
    int delaySamples = (int)(delayTimeMs * currentSampleRate / 1000.0);
    delaySamples = jmin(delaySamples, delayBufferSize - 1);
    
    for (int channel = 0; channel < buffer.getNumChannels(); ++channel)
    {
        auto* channelData = buffer.getWritePointer(channel);
        auto* delayData = delayBuffer.getWritePointer(jmin(channel, delayBuffer.getNumChannels() - 1));
        
        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            int readPosition = (writePosition - delaySamples + delayBufferSize) % delayBufferSize;
            
            float delayedSample = delayData[readPosition];
            float inputSample = channelData[sample];
            
            // Write to delay buffer with feedback
            delayData[writePosition] = inputSample + delayedSample * feedback;
            
            // Mix dry and wet
            channelData[sample] = inputSample * (1.0f - wetLevel) + delayedSample * wetLevel;
            
            writePosition = (writePosition + 1) % delayBufferSize;
        }
    }
}

void SimpleDelay::setDelayTime(float delayTimeMs)
{
    this->delayTimeMs = jlimit(1.0f, 2000.0f, delayTimeMs);
}

//==============================================================================
// SimpleLowPassFilter Implementation
void SimpleLowPassFilter::prepare(double sampleRate, int blockSize)
{
    spec.sampleRate = sampleRate;
    spec.maximumBlockSize = blockSize;
    spec.numChannels = 2;
    
    filter.prepare(spec);
    setCutoffFrequency(1000.0f);
    prepared = true;
}

void SimpleLowPassFilter::process(AudioBuffer<float>& buffer)
{
    if (!prepared) return;
    
    dsp::AudioBlock<float> block(buffer);
    dsp::ProcessContextReplacing<float> context(block);
    filter.process(context);
}

void SimpleLowPassFilter::setCutoffFrequency(float frequency)
{
    frequency = jlimit(20.0f, 20000.0f, frequency);
    auto coefficients = dsp::IIR::Coefficients<float>::makeLowPass(spec.sampleRate, frequency, 0.7f);
    filter.state = *coefficients;
}

void SimpleLowPassFilter::setResonance(float resonance)
{
    // Resonance control would require more complex filter design
    // This is a simplified version
}

//==============================================================================
// SimpleCompressor Implementation
void SimpleCompressor::prepare(double sampleRate, int blockSize)
{
    this->sampleRate = sampleRate;
    setAttack(10.0f);  // 10ms attack
    setRelease(100.0f); // 100ms release
}

void SimpleCompressor::process(AudioBuffer<float>& buffer)
{
    float thresholdLinear = dbToLinear(threshold);
    
    for (int channel = 0; channel < buffer.getNumChannels(); ++channel)
    {
        auto* channelData = buffer.getWritePointer(channel);
        
        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            float inputLevel = std::abs(channelData[sample]);
            
            // Calculate target gain reduction
            float targetGain = 1.0f;
            if (inputLevel > thresholdLinear)
            {
                float overThreshold = linearToDb(inputLevel) - threshold;
                float gainReductionDb = overThreshold * (1.0f - 1.0f / ratio);
                targetGain = dbToLinear(-gainReductionDb);
            }
            
            // Smooth gain changes
            float coeff = (targetGain < envelope) ? attackCoeff : releaseCoeff;
            envelope = targetGain + coeff * (envelope - targetGain);
            
            // Apply compression
            channelData[sample] *= envelope;
        }
    }
}

void SimpleCompressor::setAttack(float attackMs)
{
    attackCoeff = std::exp(-1.0f / (attackMs * 0.001f * sampleRate));
}

void SimpleCompressor::setRelease(float releaseMs)
{
    releaseCoeff = std::exp(-1.0f / (releaseMs * 0.001f * sampleRate));
}

//==============================================================================
// AudioEffectsChain Implementation
AudioEffectsChain::AudioEffectsChain() = default;
AudioEffectsChain::~AudioEffectsChain() = default;

void AudioEffectsChain::prepare(double sampleRate, int blockSize)
{
    reverb.prepare(sampleRate, blockSize);
    delay.prepare(sampleRate, blockSize);
    filter.prepare(sampleRate, blockSize);
    compressor.prepare(sampleRate, blockSize);
    
    tempBuffer.setSize(2, blockSize);
}

void AudioEffectsChain::process(AudioBuffer<float>& buffer)
{
    // Process effects in order: Compressor -> Filter -> Delay -> Reverb
    
    if (compressorEnabled)
    {
        compressor.process(buffer);
    }
    
    if (filterEnabled)
    {
        filter.process(buffer);
    }
    
    if (delayEnabled)
    {
        delay.process(buffer);
    }
    
    if (reverbEnabled)
    {
        reverb.process(buffer);
    }
}
