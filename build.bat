@echo off
echo Building DAW Dan with MinGW...

REM Add MSYS2 MinGW and CMake paths
set PATH=C:\msys64\mingw64\bin;C:\msys64\usr\bin;%PATH%

REM Try to find cmake and mingw32-make
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo CMake not found in PATH. Please check installation.
    pause
    exit /b 1
)

where mingw32-make >nul 2>&1
if %errorlevel% neq 0 (
    echo mingw32-make not found. Trying 'make'...
    where make >nul 2>&1
    if %errorlevel% neq 0 (
        echo Make not found in PATH. Please check MinGW installation.
        pause
        exit /b 1
    )
    set MAKE_CMD=make
) else (
    set MAKE_CMD=mingw32-make
)

REM Create build directory
if not exist build mkdir build
cd build

REM Configure with MinGW
echo Configuring with CMake...
cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release ..
if %errorlevel% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build
echo Building with %MAKE_CMD%...
%MAKE_CMD% -j4
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build complete!
pause
