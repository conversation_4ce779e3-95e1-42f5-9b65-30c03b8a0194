#include "LoopGrid.h"
#include "AudioEngine.h"

using namespace juce;

//==============================================================================
LoopGrid::LoopGrid(AudioEngine& engine, MidiEngine& midi) : audioEngine(engine), midiEngine(midi)
{
    // Create 8x8 grid of buttons (64 total)
    for (int i = 0; i < numButtons; ++i)
    {
        loopButtons[i] = std::make_unique<TextButton>();
        loopButtons[i]->setButtonText(String(i + 1));
        loopButtons[i]->addListener(this);
        addAndMakeVisible(*loopButtons[i]);
    }
    
    // Setup Launchpad integration
    setupLaunchpadCallbacks();

    // Start timer to update button states
    startTimerHz(20); // 20 FPS
}

LoopGrid::~LoopGrid()
{
    stopTimer();
}

//==============================================================================
void LoopGrid::paint(Graphics& g)
{
    // Dark background
    g.fillAll(Colour(0xff2a2a2a));

    // Title
    g.setColour(Colours::white);
    g.setFont(Font(20.0f, Font::bold));
    g.drawText("8x8 Loop Grid", getLocalBounds().removeFromTop(40),
               Justification::centred, true);
}

void LoopGrid::resized()
{
    auto area = getLocalBounds();
    area.removeFromTop(50); // Space for title
    area.reduce(20, 20); // Margins
    
    // Calculate button size
    auto buttonSize = jmin(area.getWidth(), area.getHeight()) / gridSize;
    buttonSize = jmin(buttonSize, 100); // Max button size

    // Center the grid
    auto gridWidth = buttonSize * gridSize;
    auto gridHeight = buttonSize * gridSize;
    auto gridArea = Rectangle<int>(
        area.getCentreX() - gridWidth / 2,
        area.getCentreY() - gridHeight / 2,
        gridWidth,
        gridHeight
    );
    
    // Position buttons in 4x4 grid
    for (int row = 0; row < gridSize; ++row)
    {
        for (int col = 0; col < gridSize; ++col)
        {
            int index = row * gridSize + col;
            auto buttonBounds = Rectangle<int>(
                gridArea.getX() + col * buttonSize,
                gridArea.getY() + row * buttonSize,
                buttonSize - 2, // Small gap between buttons
                buttonSize - 2
            );
            loopButtons[index]->setBounds(buttonBounds);
        }
    }
}

void LoopGrid::buttonClicked(Button* button)
{
    // Find which button was clicked
    for (int i = 0; i < numButtons; ++i)
    {
        if (loopButtons[i].get() == button)
        {
            // Check for modifier keys
            auto modifiers = ModifierKeys::getCurrentModifiers();

            if (modifiers.isShiftDown())
            {
                // Shift + click = start recording
                if (audioEngine.isLoopRecording(i))
                {
                    audioEngine.stopLoopRecording(i);
                }
                else
                {
                    audioEngine.startLoopRecording(i, false);
                }
            }
            else if (modifiers.isCtrlDown() || modifiers.isCommandDown())
            {
                // Ctrl/Cmd + click = overdub recording
                if (audioEngine.hasLoopContent(i))
                {
                    if (audioEngine.isLoopRecording(i))
                    {
                        audioEngine.stopLoopRecording(i);
                    }
                    else
                    {
                        audioEngine.startLoopRecording(i, true);
                    }
                }
            }
            else if (modifiers.isAltDown())
            {
                // Alt + click = clear loop (need to implement clearLoop)
                // audioEngine.clearLoop(i);
            }
            else
            {
                // Normal click = trigger/stop loop
                audioEngine.triggerLoop(i);
            }

            updateButtonStates();
            break;
        }
    }
}

//==============================================================================
void LoopGrid::updateButtonStates()
{
    for (int i = 0; i < numButtons; ++i)
    {
        auto& button = *loopButtons[i];
        auto colour = getButtonColour(i);
        
        button.setColour(TextButton::buttonColourId, colour);

        // Update button text based on state
        if (audioEngine.isLoopRecording(i))
        {
            button.setButtonText("● " + String(i + 1)); // Recording
        }
        else if (audioEngine.hasLoopContent(i))
        {
            if (audioEngine.isLoopPlaying(i))
                button.setButtonText("▶ " + String(i + 1)); // Playing
            else
                button.setButtonText("⏸ " + String(i + 1)); // Paused
        }
        else
        {
            button.setButtonText(String(i + 1)); // Empty
        }
    }
}

Colour LoopGrid::getButtonColour(int index) const
{
    if (audioEngine.isLoopRecording(index))
    {
        return Colours::red; // Recording
    }
    else if (audioEngine.hasLoopContent(index))
    {
        if (audioEngine.isLoopPlaying(index))
            return Colours::green; // Playing
        else
            return Colours::orange; // Has content but stopped
    }
    else
    {
        return Colours::darkgrey; // Empty
    }
}

//==============================================================================
void LoopGrid::timerCallback()
{
    updateButtonStates();
}
