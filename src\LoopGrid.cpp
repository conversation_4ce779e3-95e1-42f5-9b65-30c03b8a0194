#include "LoopGrid.h"

//==============================================================================
LoopGrid::LoopGrid(AudioEngine& engine) : audioEngine(engine)
{
    // Create 4x4 grid of buttons
    for (int i = 0; i < numButtons; ++i)
    {
        loopButtons[i] = std::make_unique<juce::TextButton>();
        loopButtons[i]->setButtonText(juce::String(i + 1));
        loopButtons[i]->addListener(this);
        addAndMakeVisible(*loopButtons[i]);
    }
    
    // Start timer to update button states
    startTimerHz(20); // 20 FPS
}

LoopGrid::~LoopGrid()
{
    stopTimer();
}

//==============================================================================
void LoopGrid::paint(juce::Graphics& g)
{
    // Dark background
    g.fillAll(juce::Colour(0xff2a2a2a));
    
    // Title
    g.setColour(juce::Colours::white);
    g.setFont(juce::Font(20.0f, juce::Font::bold));
    g.drawText("Loop Grid", getLocalBounds().removeFromTop(40), 
               juce::Justification::centred, true);
}

void LoopGrid::resized()
{
    auto area = getLocalBounds();
    area.removeFromTop(50); // Space for title
    area.reduce(20, 20); // Margins
    
    // Calculate button size
    auto buttonSize = juce::jmin(area.getWidth(), area.getHeight()) / gridSize;
    buttonSize = juce::jmin(buttonSize, 100); // Max button size
    
    // Center the grid
    auto gridWidth = buttonSize * gridSize;
    auto gridHeight = buttonSize * gridSize;
    auto gridArea = juce::Rectangle<int>(
        area.getCentreX() - gridWidth / 2,
        area.getCentreY() - gridHeight / 2,
        gridWidth,
        gridHeight
    );
    
    // Position buttons in 4x4 grid
    for (int row = 0; row < gridSize; ++row)
    {
        for (int col = 0; col < gridSize; ++col)
        {
            int index = row * gridSize + col;
            auto buttonBounds = juce::Rectangle<int>(
                gridArea.getX() + col * buttonSize,
                gridArea.getY() + row * buttonSize,
                buttonSize - 2, // Small gap between buttons
                buttonSize - 2
            );
            loopButtons[index]->setBounds(buttonBounds);
        }
    }
}

void LoopGrid::buttonClicked(juce::Button* button)
{
    // Find which button was clicked
    for (int i = 0; i < numButtons; ++i)
    {
        if (loopButtons[i].get() == button)
        {
            audioEngine.triggerLoop(i);
            updateButtonStates();
            break;
        }
    }
}

//==============================================================================
void LoopGrid::updateButtonStates()
{
    for (int i = 0; i < numButtons; ++i)
    {
        auto& button = *loopButtons[i];
        auto colour = getButtonColour(i);
        
        button.setColour(juce::TextButton::buttonColourId, colour);
        
        // Update button text based on state
        if (audioEngine.hasLoopContent(i))
        {
            if (audioEngine.isLoopPlaying(i))
                button.setButtonText("▶ " + juce::String(i + 1));
            else
                button.setButtonText("⏸ " + juce::String(i + 1));
        }
        else
        {
            button.setButtonText(juce::String(i + 1));
        }
    }
}

juce::Colour LoopGrid::getButtonColour(int index) const
{
    if (audioEngine.hasLoopContent(index))
    {
        if (audioEngine.isLoopPlaying(index))
            return juce::Colours::green; // Playing
        else
            return juce::Colours::orange; // Has content but stopped
    }
    else
    {
        return juce::Colours::darkgrey; // Empty
    }
}

//==============================================================================
void LoopGrid::timerCallback()
{
    updateButtonStates();
}
