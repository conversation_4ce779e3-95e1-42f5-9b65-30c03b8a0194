#include "AudioEngine.h"
#include <iostream>
#include <algorithm>
#include <cstring>

AudioEngine::AudioEngine() 
    : m_pDirectSound(nullptr)
    , m_pPrimaryBuffer(nullptr)
    , m_pSecondaryBuffer(nullptr)
    , m_pDirectSoundCapture(nullptr)
    , m_pCaptureBuffer(nullptr)
{
    // Initialize COM
    CoInitialize(nullptr);
}

AudioEngine::~AudioEngine() {
    Shutdown();
    CoUninitialize();
}

bool AudioEngine::Initialize() {
    std::cout << "Initializing DirectSound audio engine..." << std::endl;
    
    if (!CreateDirectSound()) {
        std::cerr << "Failed to create DirectSound!" << std::endl;
        return false;
    }
    
    if (!CreateCaptureDevice()) {
        std::cerr << "Failed to create capture device!" << std::endl;
        return false;
    }
    
    if (!CreateBuffers()) {
        std::cerr << "Failed to create audio buffers!" << std::endl;
        return false;
    }
    
    // Start audio thread
    m_shouldStop = false;
    m_audioThread = std::thread(&AudioEngine::AudioThreadProc, this);
    
    std::cout << "Audio engine initialized successfully!" << std::endl;
    return true;
}

void AudioEngine::Shutdown() {
    // Stop audio thread
    m_shouldStop = true;
    if (m_audioThread.joinable()) {
        m_audioThread.join();
    }
    
    // Release DirectSound objects
    if (m_pCaptureBuffer) {
        m_pCaptureBuffer->Release();
        m_pCaptureBuffer = nullptr;
    }
    
    if (m_pDirectSoundCapture) {
        m_pDirectSoundCapture->Release();
        m_pDirectSoundCapture = nullptr;
    }
    
    if (m_pSecondaryBuffer) {
        m_pSecondaryBuffer->Release();
        m_pSecondaryBuffer = nullptr;
    }
    
    if (m_pPrimaryBuffer) {
        m_pPrimaryBuffer->Release();
        m_pPrimaryBuffer = nullptr;
    }
    
    if (m_pDirectSound) {
        m_pDirectSound->Release();
        m_pDirectSound = nullptr;
    }
}

bool AudioEngine::CreateDirectSound() {
    HRESULT hr = DirectSoundCreate8(nullptr, &m_pDirectSound, nullptr);
    if (FAILED(hr)) return false;
    
    hr = m_pDirectSound->SetCooperativeLevel(GetDesktopWindow(), DSSCL_PRIORITY);
    return SUCCEEDED(hr);
}

bool AudioEngine::CreateCaptureDevice() {
    HRESULT hr = DirectSoundCaptureCreate8(nullptr, &m_pDirectSoundCapture, nullptr);
    return SUCCEEDED(hr);
}

bool AudioEngine::CreateBuffers() {
    WAVEFORMATEX wfx = GetWaveFormat();
    
    // Create primary buffer
    DSBUFFERDESC dsbd = {};
    dsbd.dwSize = sizeof(DSBUFFERDESC);
    dsbd.dwFlags = DSBCAPS_PRIMARYBUFFER;
    dsbd.dwBufferBytes = 0;
    dsbd.lpwfxFormat = nullptr;
    
    HRESULT hr = m_pDirectSound->CreateSoundBuffer(&dsbd, &m_pPrimaryBuffer, nullptr);
    if (FAILED(hr)) return false;
    
    hr = m_pPrimaryBuffer->SetFormat(&wfx);
    if (FAILED(hr)) return false;
    
    // Create secondary buffer for playback
    dsbd.dwFlags = DSBCAPS_GLOBALFOCUS | DSBCAPS_GETCURRENTPOSITION2;
    dsbd.dwBufferBytes = BUFFER_SIZE * sizeof(short) * CHANNELS;
    dsbd.lpwfxFormat = &wfx;
    
    hr = m_pDirectSound->CreateSoundBuffer(&dsbd, &m_pSecondaryBuffer, nullptr);
    if (FAILED(hr)) return false;
    
    // Create capture buffer
    DSCBUFFERDESC dscbd = {};
    dscbd.dwSize = sizeof(DSCBUFFERDESC);
    dscbd.dwFlags = 0;
    dscbd.dwBufferBytes = BUFFER_SIZE * sizeof(short) * CHANNELS;
    dscbd.lpwfxFormat = &wfx;
    
    LPDIRECTSOUNDCAPTUREBUFFER pTempBuffer = nullptr;
    hr = m_pDirectSoundCapture->CreateCaptureBuffer(&dscbd, &pTempBuffer, nullptr);
    if (SUCCEEDED(hr)) {
        hr = pTempBuffer->QueryInterface(IID_IDirectSoundCaptureBuffer8, (LPVOID*)&m_pCaptureBuffer);
        pTempBuffer->Release();
    }
    return SUCCEEDED(hr);
}

WAVEFORMATEX AudioEngine::GetWaveFormat() {
    WAVEFORMATEX wfx = {};
    wfx.wFormatTag = WAVE_FORMAT_PCM;
    wfx.nChannels = CHANNELS;
    wfx.nSamplesPerSec = SAMPLE_RATE;
    wfx.wBitsPerSample = BITS_PER_SAMPLE;
    wfx.nBlockAlign = (wfx.nChannels * wfx.wBitsPerSample) / 8;
    wfx.nAvgBytesPerSec = wfx.nSamplesPerSec * wfx.nBlockAlign;
    wfx.cbSize = 0;
    return wfx;
}

void AudioEngine::AudioThreadProc() {
    std::cout << "Audio thread started" << std::endl;
    
    // Start playback
    m_pSecondaryBuffer->Play(0, 0, DSBPLAY_LOOPING);
    
    short audioBuffer[BUFFER_SIZE * CHANNELS];
    
    while (!m_shouldStop) {
        // Process audio
        ProcessAudio(audioBuffer, BUFFER_SIZE);
        
        // Write to DirectSound buffer
        LPVOID lpvPtr1, lpvPtr2;
        DWORD dwBytes1, dwBytes2;
        
        HRESULT hr = m_pSecondaryBuffer->Lock(0, sizeof(audioBuffer), 
                                            &lpvPtr1, &dwBytes1, 
                                            &lpvPtr2, &dwBytes2, 
                                            DSBLOCK_ENTIREBUFFER);
        
        if (SUCCEEDED(hr)) {
            memcpy(lpvPtr1, audioBuffer, dwBytes1);
            if (lpvPtr2) {
                memcpy(lpvPtr2, (char*)audioBuffer + dwBytes1, dwBytes2);
            }
            m_pSecondaryBuffer->Unlock(lpvPtr1, dwBytes1, lpvPtr2, dwBytes2);
        }
        
        // Process recording if active
        if (m_isRecording) {
            ProcessRecording();
        }
        
        // Sleep for a bit to avoid consuming too much CPU
        Sleep(10);
    }
    
    m_pSecondaryBuffer->Stop();
    std::cout << "Audio thread stopped" << std::endl;
}

void AudioEngine::ProcessAudio(short* outputBuffer, int numSamples) {
    // Clear output buffer
    memset(outputBuffer, 0, numSamples * CHANNELS * sizeof(short));
    
    if (m_isPlaying) {
        MixLoops(outputBuffer, numSamples);
    }
}

void AudioEngine::ProcessRecording() {
    if (!m_pCaptureBuffer) return;
    
    LPVOID lpvPtr1, lpvPtr2;
    DWORD dwBytes1, dwBytes2;
    
    HRESULT hr = m_pCaptureBuffer->Lock(0, BUFFER_SIZE * CHANNELS * sizeof(short),
                                       &lpvPtr1, &dwBytes1,
                                       &lpvPtr2, &dwBytes2,
                                       DSCBLOCK_ENTIREBUFFER);
    
    if (SUCCEEDED(hr)) {
        // Copy captured audio to record buffer
        short* capturedData = static_cast<short*>(lpvPtr1);
        int samplesAvailable = dwBytes1 / (CHANNELS * sizeof(short));
        
        for (int i = 0; i < samplesAvailable && m_recordPosition < MAX_RECORD_SAMPLES; i++) {
            if (m_recordPosition >= m_recordBuffer.size()) {
                m_recordBuffer.resize(m_recordPosition + 1);
            }
            
            // Mix stereo to mono for simplicity
            m_recordBuffer[m_recordPosition] = (capturedData[i * 2] + capturedData[i * 2 + 1]) / 2;
            m_recordPosition++;
        }
        
        m_pCaptureBuffer->Unlock(lpvPtr1, dwBytes1, lpvPtr2, dwBytes2);
    }
}

void AudioEngine::MixLoops(short* outputBuffer, int numSamples) {
    for (int loopIndex = 0; loopIndex < NUM_LOOPS; loopIndex++) {
        Loop& loop = m_loops[loopIndex];
        
        if (loop.isPlaying && loop.hasContent && !loop.audioData.empty()) {
            for (int sample = 0; sample < numSamples; sample++) {
                if (loop.playPosition >= loop.audioData.size()) {
                    loop.playPosition = 0;  // Loop back to start
                }
                
                // Mix loop audio into output (stereo)
                short loopSample = static_cast<short>(loop.audioData[loop.playPosition] * loop.volume);
                outputBuffer[sample * 2] += loopSample;     // Left channel
                outputBuffer[sample * 2 + 1] += loopSample; // Right channel
                
                loop.playPosition++;
            }
        }
    }
}

// Transport controls
void AudioEngine::Play() {
    m_isPlaying = true;
    std::cout << "Transport: PLAY" << std::endl;
}

void AudioEngine::Stop() {
    m_isPlaying = false;
    
    // Reset all loop positions
    for (int i = 0; i < NUM_LOOPS; i++) {
        m_loops[i].playPosition = 0;
    }
    
    std::cout << "Transport: STOP" << std::endl;
}

void AudioEngine::StartRecording() {
    if (m_isRecording) return;
    
    m_recordBuffer.clear();
    m_recordPosition = 0;
    m_isRecording = true;
    
    // Start capture
    if (m_pCaptureBuffer) {
        m_pCaptureBuffer->Start(DSCBSTART_LOOPING);
    }
    
    std::cout << "Recording started" << std::endl;
}

void AudioEngine::StopRecording() {
    if (!m_isRecording) return;
    
    m_isRecording = false;
    
    // Stop capture
    if (m_pCaptureBuffer) {
        m_pCaptureBuffer->Stop();
    }
    
    // Find empty loop slot and copy recorded audio
    int emptySlot = FindEmptyLoop();
    if (emptySlot >= 0 && !m_recordBuffer.empty()) {
        m_loops[emptySlot].audioData = m_recordBuffer;
        m_loops[emptySlot].hasContent = true;
        m_loops[emptySlot].isPlaying = true;
        m_loops[emptySlot].playPosition = 0;
        
        std::cout << "Loop " << emptySlot << " created with " << m_recordBuffer.size() << " samples" << std::endl;
    }
    
    std::cout << "Recording stopped" << std::endl;
}

// Loop management
void AudioEngine::TriggerLoop(int index) {
    if (index < 0 || index >= NUM_LOOPS) return;
    
    Loop& loop = m_loops[index];
    if (loop.hasContent) {
        loop.isPlaying = !loop.isPlaying;
        if (loop.isPlaying) {
            loop.playPosition = 0;
        }
        std::cout << "Loop " << index << (loop.isPlaying ? " started" : " stopped") << std::endl;
    }
}

void AudioEngine::StopLoop(int index) {
    if (index < 0 || index >= NUM_LOOPS) return;
    
    m_loops[index].isPlaying = false;
    m_loops[index].playPosition = 0;
}

bool AudioEngine::IsLoopPlaying(int index) const {
    if (index < 0 || index >= NUM_LOOPS) return false;
    return m_loops[index].isPlaying;
}

bool AudioEngine::HasLoopContent(int index) const {
    if (index < 0 || index >= NUM_LOOPS) return false;
    return m_loops[index].hasContent;
}

void AudioEngine::ClearLoop(int index) {
    if (index < 0 || index >= NUM_LOOPS) return;
    
    m_loops[index].audioData.clear();
    m_loops[index].hasContent = false;
    m_loops[index].isPlaying = false;
    m_loops[index].playPosition = 0;
}

int AudioEngine::FindEmptyLoop() {
    for (int i = 0; i < NUM_LOOPS; i++) {
        if (!m_loops[i].hasContent) {
            return i;
        }
    }
    return -1;  // No empty slots
}
