#include "MainComponent.h"

using namespace juce;

//==============================================================================
MainComponent::MainComponent()
{
    // Initialize core components
    audioEngine = std::make_unique<AudioEngine>();
    midiEngine = std::make_unique<MidiEngine>();
    loopGrid = std::make_unique<LoopGrid>(*audioEngine, *midiEngine);

    // Setup transport buttons
    playButton.setButtonText("PLAY");
    playButton.addListener(this);
    playButton.setColour(TextButton::buttonColourId, Colours::green);
    addAndMakeVisible(playButton);

    stopButton.setButtonText("STOP");
    stopButton.addListener(this);
    stopButton.setColour(TextButton::buttonColourId, Colours::red);
    addAndMakeVisible(stopButton);

    recordButton.setButtonText("REC");
    recordButton.addListener(this);
    recordButton.setColour(TextButton::buttonColourId, Colours::darkred);
    addAndMakeVisible(recordButton);

    // Setup status labels
    statusLabel.setText("DAW Dan - Ready", dontSendNotification);
    statusLabel.setFont(Font(16.0f, Font::bold));
    statusLabel.setColour(Label::textColourId, Colours::white);
    addAndMakeVisible(statusLabel);

    bpmLabel.setText("120 BPM", dontSendNotification);
    bpmLabel.setFont(Font(14.0f));
    bpmLabel.setColour(Label::textColourId, Colours::lightgrey);
    addAndMakeVisible(bpmLabel);

    // Add loop grid
    addAndMakeVisible(*loopGrid);

    // Start timer for UI updates
    startTimer(50); // 20 FPS

    // Set window size
    setSize(800, 600);

    // Request audio permissions and setup audio
    if (RuntimePermissions::isRequired(RuntimePermissions::recordAudio)
        && !RuntimePermissions::isGranted(RuntimePermissions::recordAudio))
    {
        RuntimePermissions::request(RuntimePermissions::recordAudio,
                                         [&](bool granted) { setAudioChannels(granted ? 2 : 0, 2); });
    }
    else
    {
        setAudioChannels(2, 2); // 2 inputs, 2 outputs
    }
}

MainComponent::~MainComponent()
{
    shutdownAudio();
}

//==============================================================================
void MainComponent::prepareToPlay(int samplesPerBlockExpected, double sampleRate)
{
    audioEngine->prepareToPlay(samplesPerBlockExpected, sampleRate);
}

void MainComponent::getNextAudioBlock(const AudioSourceChannelInfo& bufferToFill)
{
    audioEngine->getNextAudioBlock(bufferToFill);
}

void MainComponent::releaseResources()
{
    audioEngine->releaseResources();
}

//==============================================================================
void MainComponent::paint(Graphics& g)
{
    // Dark background
    g.fillAll(Colour(0xff1a1a1a));
}

void MainComponent::resized()
{
    auto area = getLocalBounds();

    // Top status area
    auto topArea = area.removeFromTop(60);
    statusLabel.setBounds(topArea.removeFromLeft(300));
    bpmLabel.setBounds(topArea.removeFromRight(100));

    // Transport controls
    auto transportArea = area.removeFromTop(60);
    auto buttonWidth = 80;
    auto buttonHeight = 40;
    auto buttonY = (transportArea.getHeight() - buttonHeight) / 2;

    playButton.setBounds(20, buttonY, buttonWidth, buttonHeight);
    stopButton.setBounds(110, buttonY, buttonWidth, buttonHeight);
    recordButton.setBounds(200, buttonY, buttonWidth, buttonHeight);

    // Loop grid takes the rest
    loopGrid->setBounds(area);
}

//==============================================================================
void MainComponent::buttonClicked(Button* button)
{
    if (button == &playButton)
    {
        audioEngine->play();
    }
    else if (button == &stopButton)
    {
        audioEngine->stop();
    }
    else if (button == &recordButton)
    {
        if (audioEngine->isRecording())
            audioEngine->stopRecording();
        else
            audioEngine->startRecording();
    }
    else if (button == &loopModeButton)
    {
        isArrangerMode = false;
        loopModeButton.setToggleState(true, dontSendNotification);
        arrangerModeButton.setToggleState(false, dontSendNotification);
        loopGrid->setVisible(true);
        timeline->setVisible(false);
        resized(); // Update layout
    }
    else if (button == &arrangerModeButton)
    {
        isArrangerMode = true;
        loopModeButton.setToggleState(false, dontSendNotification);
        arrangerModeButton.setToggleState(true, dontSendNotification);
        loopGrid->setVisible(false);
        timeline->setVisible(true);
        resized(); // Update layout
    }
}

void MainComponent::timerCallback()
{
    // Update status
    String status = "Stopped";
    if (audioEngine->isPlaying())
        status = "Playing";
    else if (audioEngine->isRecording())
        status = "Recording";

    // Add MIDI status
    if (midiEngine->isLaunchpadConnected())
        status += " | " + midiEngine->getLaunchpadModel();
    else if (midiEngine->isMidiInputOpen())
        status += " | MIDI";

    statusLabel.setText(status, dontSendNotification);

    // Update BPM display
    bpmLabel.setText(String(audioEngine->getBPM(), 0) + " BPM", dontSendNotification);
}


