#include "MainComponent.h"

//==============================================================================
MainComponent::MainComponent()
{
    // Initialize core components
    audioEngine = std::make_unique<AudioEngine>();
    loopGrid = std::make_unique<LoopGrid>(*audioEngine);

    // Setup transport buttons
    playButton.setButtonText("PLAY");
    playButton.addListener(this);
    playButton.setColour(juce::TextButton::buttonColourId, juce::Colours::green);
    addAndMakeVisible(playButton);

    stopButton.setButtonText("STOP");
    stopButton.addListener(this);
    stopButton.setColour(juce::TextButton::buttonColourId, juce::Colours::red);
    addAndMakeVisible(stopButton);

    recordButton.setButtonText("REC");
    recordButton.addListener(this);
    recordButton.setColour(juce::TextButton::buttonColourId, juce::Colours::darkred);
    addAndMakeVisible(recordButton);

    // Setup status labels
    statusLabel.setText("DAW Dan - Ready", juce::dontSendNotification);
    statusLabel.setFont(juce::Font(16.0f, juce::Font::bold));
    statusLabel.setColour(juce::Label::textColourId, juce::Colours::white);
    addAndMakeVisible(statusLabel);

    bpmLabel.setText("120 BPM", juce::dontSendNotification);
    bpmLabel.setFont(juce::Font(14.0f));
    bpmLabel.setColour(juce::Label::textColourId, juce::Colours::lightgrey);
    addAndMakeVisible(bpmLabel);

    // Add loop grid
    addAndMakeVisible(*loopGrid);

    // Start timer for UI updates
    startTimer(50); // 20 FPS

    // Set window size
    setSize(800, 600);

    // Request audio permissions and setup audio
    if (juce::RuntimePermissions::isRequired(juce::RuntimePermissions::recordAudio)
        && !juce::RuntimePermissions::isGranted(juce::RuntimePermissions::recordAudio))
    {
        juce::RuntimePermissions::request(juce::RuntimePermissions::recordAudio,
                                         [&](bool granted) { setAudioChannels(granted ? 2 : 0, 2); });
    }
    else
    {
        setAudioChannels(2, 2); // 2 inputs, 2 outputs
    }
}

MainComponent::~MainComponent()
{
    shutdownAudio();
}

//==============================================================================
void MainComponent::prepareToPlay(int samplesPerBlockExpected, double sampleRate)
{
    audioEngine->prepareToPlay(samplesPerBlockExpected, sampleRate);
}

void MainComponent::getNextAudioBlock(const juce::AudioSourceChannelInfo& bufferToFill)
{
    audioEngine->getNextAudioBlock(bufferToFill);
}

void MainComponent::releaseResources()
{
    audioEngine->releaseResources();
}

//==============================================================================
void MainComponent::paint(juce::Graphics& g)
{
    // Dark background
    g.fillAll(juce::Colour(0xff1a1a1a));
}

void MainComponent::resized()
{
    auto area = getLocalBounds();

    // Top status area
    auto topArea = area.removeFromTop(60);
    statusLabel.setBounds(topArea.removeFromLeft(300));
    bpmLabel.setBounds(topArea.removeFromRight(100));

    // Transport controls
    auto transportArea = area.removeFromTop(60);
    auto buttonWidth = 80;
    auto buttonHeight = 40;
    auto buttonY = (transportArea.getHeight() - buttonHeight) / 2;

    playButton.setBounds(20, buttonY, buttonWidth, buttonHeight);
    stopButton.setBounds(110, buttonY, buttonWidth, buttonHeight);
    recordButton.setBounds(200, buttonY, buttonWidth, buttonHeight);

    // Loop grid takes the rest
    loopGrid->setBounds(area);
}

//==============================================================================
void MainComponent::buttonClicked(juce::Button* button)
{
    if (button == &playButton)
    {
        audioEngine->play();
    }
    else if (button == &stopButton)
    {
        audioEngine->stop();
    }
    else if (button == &recordButton)
    {
        if (audioEngine->isRecording())
            audioEngine->stopRecording();
        else
            audioEngine->startRecording();
    }
}

void MainComponent::timerCallback()
{
    // Update status
    if (audioEngine->isPlaying())
        statusLabel.setText("Playing", juce::dontSendNotification);
    else if (audioEngine->isRecording())
        statusLabel.setText("Recording", juce::dontSendNotification);
    else
        statusLabel.setText("Stopped", juce::dontSendNotification);

    // Update BPM display
    bpmLabel.setText(juce::String(audioEngine->getBPM(), 0) + " BPM", juce::dontSendNotification);
}


