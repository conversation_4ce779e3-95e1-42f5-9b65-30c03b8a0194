#include "AudioEngine.h"

//==============================================================================
AudioEngine::AudioEngine()
{
    // Initialize loop positions
    loopPositions.fill(0);
    
    // Initialize record buffer
    recordBuffer.setSize(2, maxRecordLength);
    recordBuffer.clear();
}

AudioEngine::~AudioEngine()
{
}

//==============================================================================
void AudioEngine::prepareToPlay(int samplesPerBlockExpected, double sampleRate)
{
    this->sampleRate.store(sampleRate);
    this->blockSize.store(samplesPerBlockExpected);
    
    // Prepare loops
    for (auto& loop : loops)
    {
        loop.buffer.setSize(2, static_cast<int>(sampleRate * 4.0)); // 4 second loops
        loop.buffer.clear();
    }
    
    DBG("AudioEngine prepared: " << sampleRate << "Hz, " << samplesPerBlockExpected << " samples");
}

void AudioEngine::getNextAudioBlock(const juce::AudioSourceChannelInfo& bufferToFill)
{
    // Clear the buffer first
    bufferToFill.clearActiveBufferRegion();
    
    // Process recording if active
    if (recording.load())
    {
        processRecording(bufferToFill);
    }
    
    // Process loop playback
    if (playing.load())
    {
        processPlayback(bufferToFill);
    }
}

void AudioEngine::releaseResources()
{
    DBG("AudioEngine resources released");
}

//==============================================================================
void AudioEngine::play()
{
    playing.store(true);
    DBG("Transport: PLAY");
}

void AudioEngine::stop()
{
    playing.store(false);
    recording.store(false);
    
    // Reset all loop positions
    loopPositions.fill(0);
    
    DBG("Transport: STOP");
}

void AudioEngine::startRecording()
{
    if (!recording.load())
    {
        recordPosition.store(0);
        recordLength.store(0);
        recordBuffer.clear();
        recording.store(true);
        playing.store(true); // Auto-start playback when recording
        DBG("Recording started");
    }
}

void AudioEngine::stopRecording()
{
    if (recording.load())
    {
        recording.store(false);
        
        // Find first empty loop slot and copy recorded audio
        for (int i = 0; i < numLoops; ++i)
        {
            if (!loops[i].hasContent)
            {
                auto recordedLength = recordLength.load();
                if (recordedLength > 0)
                {
                    loops[i].buffer.setSize(2, recordedLength);
                    
                    // Copy recorded audio to loop
                    for (int channel = 0; channel < 2; ++channel)
                    {
                        loops[i].buffer.copyFrom(channel, 0, 
                                               recordBuffer, channel, 0, recordedLength);
                    }
                    
                    loops[i].hasContent = true;
                    loops[i].isPlaying = true;
                    loopPositions[i] = 0;
                    
                    DBG("Loop " << i << " created with " << recordedLength << " samples");
                }
                break;
            }
        }
    }
}

//==============================================================================
void AudioEngine::triggerLoop(int index)
{
    if (index >= 0 && index < numLoops)
    {
        if (loops[index].hasContent)
        {
            loops[index].isPlaying = !loops[index].isPlaying;
            if (loops[index].isPlaying)
            {
                loopPositions[index] = 0; // Reset position when starting
            }
            DBG("Loop " << index << (loops[index].isPlaying ? " started" : " stopped"));
        }
    }
}

void AudioEngine::stopLoop(int index)
{
    if (index >= 0 && index < numLoops)
    {
        loops[index].isPlaying = false;
        loopPositions[index] = 0;
        DBG("Loop " << index << " stopped");
    }
}

bool AudioEngine::isLoopPlaying(int index) const
{
    if (index >= 0 && index < numLoops)
        return loops[index].isPlaying;
    return false;
}

bool AudioEngine::hasLoopContent(int index) const
{
    if (index >= 0 && index < numLoops)
        return loops[index].hasContent;
    return false;
}

//==============================================================================
void AudioEngine::processRecording(const juce::AudioSourceChannelInfo& bufferToFill)
{
    auto numSamples = bufferToFill.numSamples;
    auto currentRecordPos = recordPosition.load();
    
    // Check if we have space to record
    if (currentRecordPos + numSamples <= maxRecordLength)
    {
        // Copy input to record buffer
        for (int channel = 0; channel < juce::jmin(2, bufferToFill.buffer->getNumChannels()); ++channel)
        {
            recordBuffer.copyFrom(channel, currentRecordPos,
                                *bufferToFill.buffer, channel, 
                                bufferToFill.startSample, numSamples);
        }
        
        recordPosition.store(currentRecordPos + numSamples);
        recordLength.store(currentRecordPos + numSamples);
        
        // Pass through input for monitoring
        // (Input is already in the buffer from the audio device)
    }
    else
    {
        // Auto-stop recording when buffer is full
        stopRecording();
    }
}

void AudioEngine::processPlayback(const juce::AudioSourceChannelInfo& bufferToFill)
{
    processLoops(*bufferToFill.buffer);
}

void AudioEngine::processLoops(juce::AudioBuffer<float>& buffer)
{
    auto numSamples = buffer.getNumSamples();
    
    for (int loopIndex = 0; loopIndex < numLoops; ++loopIndex)
    {
        auto& loop = loops[loopIndex];
        
        if (loop.isPlaying && loop.hasContent)
        {
            auto& loopBuffer = loop.buffer;
            auto loopLength = loopBuffer.getNumSamples();
            auto& position = loopPositions[loopIndex];
            
            if (loopLength > 0)
            {
                for (int sample = 0; sample < numSamples; ++sample)
                {
                    if (position >= loopLength)
                        position = 0; // Loop back to start
                    
                    // Mix loop audio into output buffer
                    for (int channel = 0; channel < juce::jmin(2, buffer.getNumChannels()); ++channel)
                    {
                        if (channel < loopBuffer.getNumChannels())
                        {
                            auto loopSample = loopBuffer.getSample(channel, position);
                            auto currentSample = buffer.getSample(channel, sample);
                            buffer.setSample(channel, sample, currentSample + loopSample * loop.volume);
                        }
                    }
                    
                    ++position;
                }
            }
        }
    }
}
