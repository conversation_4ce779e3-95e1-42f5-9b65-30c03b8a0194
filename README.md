# DAW Dan - Live Looping Digital Audio Workstation

A cross-platform live looping DAW built with C++ and JUCE, featuring real-time audio recording, MIDI integration, and touch instruments.

## Features

### Core Features
- **Live Looping Grid**: 8x8 grid for real-time loop recording and playback
- **Arranger Mode**: Timeline-based song arrangement with drag-and-drop
- **Touch Instruments**: Virtual keyboard, drum pads, and synthesizers
- **MIDI Integration**: Full MIDI support with Launchpad controller integration
- **Cross-Platform**: Runs on Windows, macOS, and Linux

### Audio Features
- Real-time audio recording with automatic quantization
- Low-latency audio playback and monitoring
- Built-in audio effects (reverb, delay, filters)
- Multi-track recording and playback
- Audio file import/export (WAV, AIFF, MP3)

### MIDI Features
- MIDI device auto-detection and configuration
- Launchpad Pro/Mini/X integration with LED feedback
- MIDI learn for custom controller mapping
- MIDI clock synchronization
- MIDI recording and playback

## Building from Source

### Prerequisites
- CMake 3.22 or higher
- C++17 compatible compiler (GCC 7+, Clang 5+, MSVC 2019+)
- Git (for JUCE submodule)

### Dependencies
- JUCE Framework 7.0+ (included as submodule)

### Build Instructions

#### Clone the Repository
```bash
git clone https://github.com/your-username/daw-dan.git
cd daw-dan
git submodule update --init --recursive
```

#### Windows (Visual Studio)
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022"
cmake --build . --config Release
```

#### macOS
```bash
mkdir build
cd build
cmake .. -G Xcode
cmake --build . --config Release
```

#### Linux
```bash
mkdir build
cd build
cmake ..
make -j$(nproc)
```

### Installing Dependencies

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install build-essential cmake git
sudo apt install libasound2-dev libjack-jackd2-dev
sudo apt install libfreetype6-dev libx11-dev libxinerama-dev libxrandr-dev libxcursor-dev libxcomposite-dev
```

#### macOS
```bash
# Install Xcode Command Line Tools
xcode-select --install

# Install CMake (via Homebrew)
brew install cmake
```

#### Windows
- Install Visual Studio 2019 or later with C++ development tools
- Install CMake from https://cmake.org/download/
- Install Git from https://git-scm.com/download/win

## Usage

### Getting Started
1. Launch DAW Dan
2. Configure your audio device in Settings
3. Connect MIDI controllers (optional)
4. Start recording loops in the Live Loops grid
5. Arrange your loops in the Arranger view

### Live Looping
- Click empty cells in the 8x8 grid to start recording
- Click again to stop recording and start playback
- Use keyboard shortcuts for quick access:
  - `Space`: Play/Pause transport
  - `R`: Start/Stop recording
  - `S`: Stop all loops
  - `M`: Mute selected track

### MIDI Controllers
- Connect a Launchpad for hardware control
- Use MIDI Learn to map any MIDI controller
- Configure MIDI settings in the Settings panel

### Touch Instruments
- Access virtual instruments from the instrument panel
- Play using mouse/touch or MIDI keyboard
- Record instrument performances directly to loops

## Project Structure

```
src/
├── Main.cpp                    # Application entry point
├── MainComponent.h/cpp         # Main application window
├── audio/                      # Audio engine
├── midi/                       # MIDI handling
├── ui/                         # User interface components
├── core/                       # Core application logic
└── utils/                      # Utility functions
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the GPL v3 License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with [JUCE Framework](https://juce.com/)
- Inspired by GarageBand Live Loops and Ableton Live
- Thanks to the JUCE community for excellent documentation and examples

## Support

- Report bugs and request features on [GitHub Issues](https://github.com/your-username/daw-dan/issues)
- Join our community discussions
- Check the [Wiki](https://github.com/your-username/daw-dan/wiki) for detailed documentation
