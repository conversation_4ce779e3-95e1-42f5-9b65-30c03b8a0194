Write-Host "DAW Dan - JUCE Build Script" -ForegroundColor Green
Write-Host ""

# Set environment
$env:PATH = "C:\msys64\mingw64\bin;C:\msys64\usr\bin;" + $env:PATH

# Check tools
Write-Host "Checking build tools..." -ForegroundColor Yellow
try {
    $cmakeVersion = cmake --version 2>$null
    Write-Host "CMake found" -ForegroundColor Green
} catch {
    Write-Host "CMake not found!" -ForegroundColor Red
    exit 1
}

try {
    $gccVersion = gcc --version 2>$null
    Write-Host "GCC found" -ForegroundColor Green
} catch {
    Write-Host "GCC not found!" -ForegroundColor Red
    exit 1
}

# Create build directory
Write-Host ""
Write-Host "Setting up build directory..." -ForegroundColor Yellow
if (Test-Path "build_ps") {
    Remove-Item "build_ps" -Recurse -Force
}
New-Item -ItemType Directory -Name "build_ps" | Out-Null
Set-Location "build_ps"

# Configure with CMake
Write-Host ""
Write-Host "Configuring CMake..." -ForegroundColor Yellow
cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release ..

if ($LASTEXITCODE -ne 0) {
    Write-Host "CMake configuration failed!" -ForegroundColor Red
    exit 1
}

# Build
Write-Host ""
Write-Host "Building DAW Dan..." -ForegroundColor Yellow
mingw32-make -j2

if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed! Trying single-threaded..." -ForegroundColor Yellow
    mingw32-make

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed completely!" -ForegroundColor Red
        exit 1
    }
}

# Check result
Write-Host ""
if (Test-Path "bin\DAWDan.exe") {
    Write-Host "Build successful!" -ForegroundColor Green
    Write-Host "Executable: build_ps\bin\DAWDan.exe" -ForegroundColor Cyan
    Write-Host ""
    $choice = Read-Host "Launch DAW Dan now? (y/n)"
    if ($choice -eq "y" -or $choice -eq "Y") {
        Start-Process "bin\DAWDan.exe"
    }
} else {
    Write-Host "Executable not found. Checking build directory..." -ForegroundColor Yellow
    Get-ChildItem -Recurse -Filter "*.exe"
}

Write-Host ""
Write-Host "Build process complete!" -ForegroundColor Green
