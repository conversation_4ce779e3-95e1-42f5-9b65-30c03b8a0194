@echo off
echo Building DAW Dan Simple (DirectSound version)...

REM Add MSYS2 MinGW paths
set PATH=C:\msys64\mingw64\bin;C:\msys64\usr\bin;%PATH%

REM Clean previous build
if exist build_simple rmdir /s /q build_simple
mkdir build_simple

REM Copy CMake file
copy CMakeLists_Simple.txt CMakeLists.txt

cd build_simple

REM Configure with CMake
echo Configuring...
cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release ..

if %errorlevel% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build (should be very fast)
echo Building...
mingw32-make -j4

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build complete! DAWDanSimple.exe is ready.
echo.
echo To run: .\DAWDanSimple.exe
echo.
pause
