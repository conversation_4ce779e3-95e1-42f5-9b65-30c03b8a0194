^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\DAW DAN\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SC:/Users/<USER>/Documents/augment-projects/DAW Dan" "-BC:/Users/<USER>/Documents/augment-projects/DAW Dan/build_vs" --check-stamp-file "C:/Users/<USER>/Documents/augment-projects/DAW Dan/build_vs/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
