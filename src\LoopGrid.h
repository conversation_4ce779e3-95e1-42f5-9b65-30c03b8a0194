#pragma once

#include <JuceHeader.h>
#include "AudioEngine.h"

//==============================================================================
// Sprint Version - Simple 4x4 Loop Grid
class LoopGrid : public juce::Component,
                 public juce::Button::Listener,
                 public juce::Timer
{
public:
    //==============================================================================
    LoopGrid(AudioEngine& audioEngine);
    ~LoopGrid() override;

    //==============================================================================
    void paint(juce::Graphics& g) override;
    void resized() override;
    void buttonClicked(juce::Button* button) override;
    void timerCallback() override;

private:
    //==============================================================================
    AudioEngine& audioEngine;
    
    //==============================================================================
    // 4x4 grid = 16 buttons
    static constexpr int gridSize = 4;
    static constexpr int numButtons = gridSize * gridSize;
    
    std::array<std::unique_ptr<juce::TextButton>, numButtons> loopButtons;
    
    //==============================================================================
    void updateButtonStates();
    juce::Colour getButtonColour(int index) const;
    
    //==============================================================================
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(LoopGrid)
};
