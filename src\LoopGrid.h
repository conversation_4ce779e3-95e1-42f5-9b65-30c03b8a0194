#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_events/juce_events.h>
#include "AudioEngine.h"
#include "MidiEngine.h"

using namespace juce;

//==============================================================================
// Sprint Version - Simple 4x4 Loop Grid
class LoopGrid : public Component,
                 public Button::Listener,
                 public Timer
{
public:
    //==============================================================================
    LoopGrid(AudioEngine& audioEngine, MidiEngine& midiEngine);
    ~LoopGrid() override;

    //==============================================================================
    void paint(Graphics& g) override;
    void resized() override;
    void buttonClicked(Button* button) override;
    void timerCallback() override;

private:
    //==============================================================================
    AudioEngine& audioEngine;
    MidiEngine& midiEngine;
    
    //==============================================================================
    // 8x8 grid = 64 buttons (GarageBand Live Loops style)
    static constexpr int gridSize = 8;
    static constexpr int numButtons = gridSize * gridSize;
    
    std::array<std::unique_ptr<TextButton>, numButtons> loopButtons;
    
    //==============================================================================
    void updateButtonStates();
    void setupLaunchpadCallbacks();
    void syncLaunchpadLEDs();
    Colour getButtonColour(int index) const;
    
    //==============================================================================
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(LoopGrid)
};
