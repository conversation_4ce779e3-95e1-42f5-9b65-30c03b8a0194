#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_events/juce_events.h>
#include "AudioEngine.h"

using namespace juce;

//==============================================================================
// Sprint Version - Simple 4x4 Loop Grid
class LoopGrid : public Component,
                 public Button::Listener,
                 public Timer
{
public:
    //==============================================================================
    LoopGrid(AudioEngine& audioEngine);
    ~LoopGrid() override;

    //==============================================================================
    void paint(Graphics& g) override;
    void resized() override;
    void buttonClicked(Button* button) override;
    void timerCallback() override;

private:
    //==============================================================================
    AudioEngine& audioEngine;
    
    //==============================================================================
    // 4x4 grid = 16 buttons
    static constexpr int gridSize = 4;
    static constexpr int numButtons = gridSize * gridSize;
    
    std::array<std::unique_ptr<TextButton>, numButtons> loopButtons;
    
    //==============================================================================
    void updateButtonStates();
    Colour getButtonColour(int index) const;
    
    //==============================================================================
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(LoopGrid)
};
