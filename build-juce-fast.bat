@echo off
title DAW Dan - Fast JUCE Build
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    DAW DAN - FAST JUCE BUILD                ║
echo  ║                   Using Existing JUCE Download              ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Add MSYS2 MinGW and CMake paths
set PATH=C:\msys64\mingw64\bin;C:\msys64\usr\bin;%PATH%

echo 🚀 Using existing JUCE build directory...

REM Go to existing build directory
if not exist build_juce (
    echo ❌ No existing build directory found!
    echo Please run build-juce.bat first to download JUCE.
    pause
    exit /b 1
)

cd build_juce

echo.
echo 🔨 Building DAW Dan (continuing from where we left off)...

REM Continue the build process
mingw32-make -j4

if %errorlevel% neq 0 (
    echo ❌ Build failed!
    echo.
    echo Trying single-threaded build for better error visibility...
    mingw32-make
    
    if %errorlevel% neq 0 (
        echo ❌ Single-threaded build also failed!
        pause
        exit /b 1
    )
)

echo.
echo ✅ Build successful!
echo.

if exist "bin\DAWDan.exe" (
    echo 🎵 DAW Dan is ready!
    echo 📁 Location: build_juce\bin\DAWDan.exe
    echo.
    echo Would you like to run it now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 🚀 Launching DAW Dan...
        start "DAW Dan" "bin\DAWDan.exe"
    )
) else (
    echo ⚠️  Checking for executable in other locations...
    dir /s *.exe | findstr DAWDan
)

echo.
pause
