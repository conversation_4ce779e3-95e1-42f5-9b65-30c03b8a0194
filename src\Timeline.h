#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_audio_basics/juce_audio_basics.h>

using namespace juce;

//==============================================================================
/**
 * Represents a clip on the timeline
 */
struct TimelineClip
{
    int trackIndex = 0;
    double startTime = 0.0;
    double duration = 4.0;
    int loopIndex = -1;
    String name = "Clip";
    Colour colour = Colours::blue;
    bool selected = false;
};

//==============================================================================
/**
 * Represents a track in the arranger
 */
struct ArrangerTrack
{
    String name = "Track";
    bool muted = false;
    bool soloed = false;
    float volume = 1.0f;
    float pan = 0.0f;
    Colour colour = Colours::grey;
    std::vector<TimelineClip> clips;
};

//==============================================================================
/**
 * Timeline Component for song arrangement
 */
class Timeline : public Component,
                 public DragAndDropContainer,
                 public DragAndDropTarget
{
public:
    //==============================================================================
    Timeline();
    ~Timeline() override;

    //==============================================================================
    void paint(Graphics& g) override;
    void resized() override;
    void mouseDown(const MouseEvent& e) override;
    void mouseDrag(const MouseEvent& e) override;
    void mouseUp(const MouseEvent& e) override;

    //==============================================================================
    // Drag and Drop
    bool isInterestedInDragSource(const SourceDetails& dragSourceDetails) override;
    void itemDropped(const SourceDetails& dragSourceDetails) override;

    //==============================================================================
    // Timeline control
    void setPlayheadPosition(double timeInSeconds);
    double getPlayheadPosition() const { return playheadPosition; }
    
    void setZoom(double pixelsPerSecond);
    double getZoom() const { return zoom; }
    
    void setViewStart(double timeInSeconds);
    double getViewStart() const { return viewStart; }

    //==============================================================================
    // Track management
    void addTrack(const String& name = "New Track");
    void removeTrack(int trackIndex);
    int getNumTracks() const { return (int)tracks.size(); }
    ArrangerTrack& getTrack(int index) { return tracks[index]; }

    //==============================================================================
    // Clip management
    void addClip(int trackIndex, double startTime, double duration, int loopIndex);
    void removeClip(int trackIndex, int clipIndex);
    void selectClip(int trackIndex, int clipIndex);
    void clearSelection();

    //==============================================================================
    // Callbacks
    std::function<void(double)> onPlayheadMoved;
    std::function<void(int, int)> onClipSelected; // trackIndex, clipIndex
    std::function<void(int, const TimelineClip&)> onClipMoved;

private:
    //==============================================================================
    // Timeline properties
    double playheadPosition = 0.0;
    double zoom = 50.0; // pixels per second
    double viewStart = 0.0;
    static constexpr int trackHeight = 60;
    static constexpr int headerWidth = 150;

    //==============================================================================
    // Tracks and clips
    std::vector<ArrangerTrack> tracks;
    int selectedTrack = -1;
    int selectedClip = -1;

    //==============================================================================
    // Interaction
    bool draggingPlayhead = false;
    bool draggingClip = false;
    Point<int> dragStartPosition;
    TimelineClip* draggedClip = nullptr;

    //==============================================================================
    // Helper methods
    double pixelToTime(int pixel) const;
    int timeToPixel(double time) const;
    Rectangle<int> getClipBounds(const TimelineClip& clip, int trackIndex) const;
    Rectangle<int> getTrackBounds(int trackIndex) const;
    void drawTimeRuler(Graphics& g, Rectangle<int> area);
    void drawTracks(Graphics& g, Rectangle<int> area);
    void drawPlayhead(Graphics& g, Rectangle<int> area);

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(Timeline)
};
