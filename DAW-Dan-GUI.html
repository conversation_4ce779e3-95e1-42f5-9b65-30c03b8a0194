<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAW Dan - Live Looping Studio</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            text-align: center;
        }
        
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .card h3 {
            color: #4CAF50;
            margin-top: 0;
            font-size: 1.4em;
        }
        
        .controls {
            text-align: left;
            line-height: 1.8;
        }
        
        .controls strong {
            color: #4CAF50;
            font-family: monospace;
            background: rgba(76, 175, 80, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .launch-section {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        .launch-btn {
            background: linear-gradient(45deg, #FF5722, #F44336);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        .launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        }
        
        .status {
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        
        .loop-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            max-width: 400px;
            margin: 20px auto;
        }
        
        .loop-button {
            background: linear-gradient(45deg, #424242, #616161);
            border: 2px solid #4CAF50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        .loop-button:hover {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            transform: scale(1.05);
        }
        
        .footer {
            margin-top: 50px;
            opacity: 0.7;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 DAW Dan</h1>
            <p>Live Looping Studio - Professional Audio Engine</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>🎛️ Features</h3>
                <ul style="text-align: left;">
                    <li>Real-time audio recording & playback</li>
                    <li>16 independent loop slots (4×4 grid)</li>
                    <li>DirectSound audio engine</li>
                    <li>Professional 44.1kHz quality</li>
                    <li>Instant loop triggering</li>
                    <li>Live performance ready</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🎹 Controls</h3>
                <div class="controls">
                    <strong>P</strong> - Play/Stop transport<br>
                    <strong>R</strong> - Start/Stop recording<br>
                    <strong>1-9, 0</strong> - Trigger loops 1-10<br>
                    <strong>S</strong> - Show status<br>
                    <strong>Q</strong> - Quit application<br>
                    <strong>Space</strong> - Quick play/stop
                </div>
            </div>
            
            <div class="card">
                <h3>🚀 Quick Start</h3>
                <ol style="text-align: left;">
                    <li>Launch the console below</li>
                    <li>Press <strong>R</strong> to start recording</li>
                    <li>Make some sound (speak, sing, clap)</li>
                    <li>Press <strong>R</strong> again to stop</li>
                    <li>Press <strong>1</strong> to trigger your loop</li>
                    <li>Press <strong>P</strong> to start playback</li>
                </ol>
            </div>
        </div>
        
        <div class="launch-section">
            <h2>🎵 Launch DAW Dan</h2>
            <p>Click the button below to start the audio engine</p>
            <button class="launch-btn" onclick="launchDAW()">🚀 Launch Console</button>
            <button class="launch-btn" onclick="openFolder()">📁 Open Project Folder</button>
        </div>
        
        <div class="card">
            <h3>🎛️ Virtual Loop Grid</h3>
            <p>This represents your 4×4 loop grid (use number keys in console)</p>
            <div class="loop-grid">
                <div class="loop-button" onclick="showLoopInfo(1)">1</div>
                <div class="loop-button" onclick="showLoopInfo(2)">2</div>
                <div class="loop-button" onclick="showLoopInfo(3)">3</div>
                <div class="loop-button" onclick="showLoopInfo(4)">4</div>
                <div class="loop-button" onclick="showLoopInfo(5)">5</div>
                <div class="loop-button" onclick="showLoopInfo(6)">6</div>
                <div class="loop-button" onclick="showLoopInfo(7)">7</div>
                <div class="loop-button" onclick="showLoopInfo(8)">8</div>
                <div class="loop-button" onclick="showLoopInfo(9)">9</div>
                <div class="loop-button" onclick="showLoopInfo(10)">0</div>
                <div class="loop-button" onclick="showLoopInfo(11)">11</div>
                <div class="loop-button" onclick="showLoopInfo(12)">12</div>
                <div class="loop-button" onclick="showLoopInfo(13)">13</div>
                <div class="loop-button" onclick="showLoopInfo(14)">14</div>
                <div class="loop-button" onclick="showLoopInfo(15)">15</div>
                <div class="loop-button" onclick="showLoopInfo(16)">16</div>
            </div>
        </div>
        
        <div class="status">
            <h3>📊 System Status</h3>
            <p><strong>Audio Engine:</strong> DirectSound (Windows)</p>
            <p><strong>Sample Rate:</strong> 44.1 kHz</p>
            <p><strong>Bit Depth:</strong> 16-bit</p>
            <p><strong>Channels:</strong> Stereo</p>
            <p><strong>Loop Slots:</strong> 16 (4×4 grid)</p>
        </div>
        
        <div class="footer">
            <p>DAW Dan - Built with C++ and DirectSound | Professional Live Looping Studio</p>
        </div>
    </div>
    
    <script>
        function launchDAW() {
            alert('🎵 Launching DAW Dan Console!\n\nThe console window will open separately.\nUse the keyboard controls listed above to operate the DAW.\n\nNote: Make sure DAWDanSimple.exe is built first!');
            
            // Try to open the executable (this will only work in some browsers/contexts)
            try {
                window.open('build_vs/Release/DAWDanSimple.exe', '_blank');
            } catch (e) {
                // Fallback: show instructions
                alert('Please manually run:\nbuild_vs\\Release\\DAWDanSimple.exe\n\nOr use the batch launcher: DAW-Dan-GUI.bat');
            }
        }
        
        function openFolder() {
            alert('📁 Opening project folder...\n\nPlease navigate to your DAW Dan project directory manually.');
        }
        
        function showLoopInfo(loopNum) {
            const key = loopNum === 10 ? '0' : loopNum.toString();
            alert(`🎵 Loop ${loopNum}\n\nPress '${key}' in the console to trigger this loop.\n\nFirst record something with 'R', then trigger with '${key}'!`);
        }
        
        // Add some visual effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });
        
        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
