#pragma once

#include <juce_audio_basics/juce_audio_basics.h>
#include <juce_dsp/juce_dsp.h>

using namespace juce;

//==============================================================================
/**
 * Simple Reverb Effect
 */
class SimpleReverb
{
public:
    void prepare(double sampleRate, int blockSize);
    void process(AudioBuffer<float>& buffer);
    void setRoomSize(float roomSize) { reverb.setParameters({roomSize, 0.5f, 0.3f, 0.4f, 0.0f, 0.0f}); }
    void setWetLevel(float wetLevel) { this->wetLevel = wetLevel; }

private:
    Reverb reverb;
    float wetLevel = 0.3f;
    AudioBuffer<float> wetBuffer;
};

//==============================================================================
/**
 * Simple Delay Effect
 */
class SimpleDelay
{
public:
    void prepare(double sampleRate, int blockSize);
    void process(AudioBuffer<float>& buffer);
    void setDelayTime(float delayTimeMs);
    void setFeedback(float feedback) { this->feedback = jlimit(0.0f, 0.95f, feedback); }
    void setWetLevel(float wetLevel) { this->wetLevel = wetLevel; }

private:
    AudioBuffer<float> delayBuffer;
    int delayBufferSize = 0;
    int writePosition = 0;
    float delayTimeMs = 250.0f;
    float feedback = 0.4f;
    float wetLevel = 0.3f;
    double currentSampleRate = 44100.0;
};

//==============================================================================
/**
 * Simple Low-Pass Filter
 */
class SimpleLowPassFilter
{
public:
    void prepare(double sampleRate, int blockSize);
    void process(AudioBuffer<float>& buffer);
    void setCutoffFrequency(float frequency);
    void setResonance(float resonance);

private:
    dsp::ProcessorDuplicator<dsp::IIR::Filter<float>, dsp::IIR::Coefficients<float>> filter;
    dsp::ProcessSpec spec;
    bool prepared = false;
};

//==============================================================================
/**
 * Simple Compressor
 */
class SimpleCompressor
{
public:
    void prepare(double sampleRate, int blockSize);
    void process(AudioBuffer<float>& buffer);
    void setThreshold(float threshold) { this->threshold = threshold; }
    void setRatio(float ratio) { this->ratio = jmax(1.0f, ratio); }
    void setAttack(float attackMs);
    void setRelease(float releaseMs);

private:
    float threshold = -12.0f; // dB
    float ratio = 4.0f;
    float attackCoeff = 0.0f;
    float releaseCoeff = 0.0f;
    float envelope = 0.0f;
    double sampleRate = 44100.0;
    
    float dbToLinear(float db) { return std::pow(10.0f, db / 20.0f); }
    float linearToDb(float linear) { return 20.0f * std::log10(jmax(0.0001f, linear)); }
};

//==============================================================================
/**
 * Audio Effects Chain
 */
class AudioEffectsChain
{
public:
    AudioEffectsChain();
    ~AudioEffectsChain();

    void prepare(double sampleRate, int blockSize);
    void process(AudioBuffer<float>& buffer);

    // Effect controls
    void setReverbEnabled(bool enabled) { reverbEnabled = enabled; }
    void setDelayEnabled(bool enabled) { delayEnabled = enabled; }
    void setFilterEnabled(bool enabled) { filterEnabled = enabled; }
    void setCompressorEnabled(bool enabled) { compressorEnabled = enabled; }

    // Effect parameters
    SimpleReverb& getReverb() { return reverb; }
    SimpleDelay& getDelay() { return delay; }
    SimpleLowPassFilter& getFilter() { return filter; }
    SimpleCompressor& getCompressor() { return compressor; }

private:
    SimpleReverb reverb;
    SimpleDelay delay;
    SimpleLowPassFilter filter;
    SimpleCompressor compressor;

    bool reverbEnabled = false;
    bool delayEnabled = false;
    bool filterEnabled = false;
    bool compressorEnabled = false;

    AudioBuffer<float> tempBuffer;
};
