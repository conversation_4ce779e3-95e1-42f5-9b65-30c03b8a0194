#include "Window.h"
#include <iostream>

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    std::cout << "Starting DAW Dan Simple..." << std::endl;
    
    try {
        Window window(hInstance);
        
        if (!window.Create(L"DAW Dan - Simple Version", 800, 600)) {
            std::cerr << "Failed to create window!" << std::endl;
            return -1;
        }
        
        window.Show(nCmdShow);
        
        // Message loop
        MSG msg = {};
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        return static_cast<int>(msg.wParam);
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return -1;
    }
}
