#pragma once

#include <juce_audio_basics/juce_audio_basics.h>
#include <juce_audio_devices/juce_audio_devices.h>
#include <juce_audio_utils/juce_audio_utils.h>
#include <memory>
#include <vector>
#include <atomic>

using namespace juce;

//==============================================================================
// Sprint Version - Simplified AudioEngine
class AudioEngine : public AudioSource
{
public:
    //==============================================================================
    AudioEngine();
    ~AudioEngine() override;

    //==============================================================================
    // AudioSource interface
    void prepareToPlay(int samplesPerBlockExpected, double sampleRate) override;
    void getNextAudioBlock(const AudioSourceChannelInfo& bufferToFill) override;
    void releaseResources() override;

    //==============================================================================
    // Transport controls
    void play();
    void stop();
    void startRecording();
    void stopRecording();
    
    bool isPlaying() const { return playing.load(); }
    bool isRecording() const { return recording.load(); }
    double getBPM() const { return bpm.load(); }

    //==============================================================================
    // Loop management (simplified)
    struct Loop
    {
        AudioBuffer<float> buffer;
        bool isPlaying = false;
        bool isRecording = false;
        bool hasContent = false;
        bool overdubMode = false;
        float volume = 1.0f;
        int length = 0; // Length in samples
        int recordStartSample = 0; // For quantization
    };
    
    void triggerLoop(int index);
    void stopLoop(int index);
    void clearLoop(int index);
    void startLoopRecording(int index, bool overdub = false);
    void stopLoopRecording(int index);
    bool isLoopPlaying(int index) const;
    bool isLoopRecording(int index) const;
    bool hasLoopContent(int index) const;

private:
    //==============================================================================
    // Audio parameters
    std::atomic<double> sampleRate { 44100.0 };
    std::atomic<int> blockSize { 512 };
    std::atomic<bool> playing { false };
    std::atomic<bool> recording { false };
    std::atomic<double> bpm { 120.0 };
    std::atomic<int> globalSamplePosition { 0 };
    int samplesPerBeat = 0;
    int samplesPerBar = 0;
    bool quantizeRecording = true;
    
    //==============================================================================
    // Recording
    AudioBuffer<float> recordBuffer;
    std::atomic<int> recordPosition { 0 };
    std::atomic<int> recordLength { 0 };
    static constexpr int maxRecordLength = 44100 * 4; // 4 seconds at 44.1kHz
    
    //==============================================================================
    // Loops (8x8 = 64 loops like GarageBand Live Loops)
    static constexpr int numLoops = 64;
    std::array<Loop, numLoops> loops;
    std::array<int, numLoops> loopPositions;
    
    //==============================================================================
    // Audio processing
    void processRecording(const AudioSourceChannelInfo& bufferToFill);
    void processPlayback(const AudioSourceChannelInfo& bufferToFill);
    void processLoops(AudioBuffer<float>& buffer);
    void createDemoLoop();
    
    //==============================================================================
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AudioEngine)
};
