#pragma once

#include <JuceHeader.h>
#include <memory>
#include <vector>
#include <atomic>

//==============================================================================
// Sprint Version - Simplified AudioEngine
class AudioEngine : public juce::AudioSource
{
public:
    //==============================================================================
    AudioEngine();
    ~AudioEngine() override;

    //==============================================================================
    // AudioSource interface
    void prepareToPlay(int samplesPerBlockExpected, double sampleRate) override;
    void getNextAudioBlock(const juce::AudioSourceChannelInfo& bufferToFill) override;
    void releaseResources() override;

    //==============================================================================
    // Transport controls
    void play();
    void stop();
    void startRecording();
    void stopRecording();
    
    bool isPlaying() const { return playing.load(); }
    bool isRecording() const { return recording.load(); }
    double getBPM() const { return bpm.load(); }

    //==============================================================================
    // Loop management (simplified)
    struct Loop
    {
        juce::AudioBuffer<float> buffer;
        bool isPlaying = false;
        bool hasContent = false;
        float volume = 1.0f;
    };
    
    void triggerLoop(int index);
    void stopLoop(int index);
    bool isLoopPlaying(int index) const;
    bool hasLoopContent(int index) const;

private:
    //==============================================================================
    // Audio parameters
    std::atomic<double> sampleRate { 44100.0 };
    std::atomic<int> blockSize { 512 };
    std::atomic<bool> playing { false };
    std::atomic<bool> recording { false };
    std::atomic<double> bpm { 120.0 };
    
    //==============================================================================
    // Recording
    juce::AudioBuffer<float> recordBuffer;
    std::atomic<int> recordPosition { 0 };
    std::atomic<int> recordLength { 0 };
    static constexpr int maxRecordLength = 44100 * 4; // 4 seconds at 44.1kHz
    
    //==============================================================================
    // Loops (4x4 = 16 loops for sprint)
    static constexpr int numLoops = 16;
    std::array<Loop, numLoops> loops;
    std::array<int, numLoops> loopPositions;
    
    //==============================================================================
    // Audio processing
    void processRecording(const juce::AudioSourceChannelInfo& bufferToFill);
    void processPlayback(const juce::AudioSourceChannelInfo& bufferToFill);
    void processLoops(juce::AudioBuffer<float>& buffer);
    
    //==============================================================================
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AudioEngine)
};
