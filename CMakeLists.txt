cmake_minimum_required(VERSION 3.22)

# Project configuration
project(DAWDan VERSION 2.0.0 LANGUAGES CXX)

# Modern C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# JUCE setup - Download JUCE if not already available
include(FetchContent)
FetchContent_Declare(
    JUCE
    GIT_REPOSITORY https://github.com/juce-framework/JUCE.git
    GIT_TAG 7.0.12
    GIT_SHALLOW TRUE
)
FetchContent_MakeAvailable(JUCE)

# Create the main JUCE application
juce_add_gui_app(DAWDan
    COMPANY_NAME "DAW Dan Studio"
    PRODUCT_NAME "DAW Dan"
    VERSION 2.0.0
    BUNDLE_ID com.dawdan.studio
    ICON_BIG ""
    ICON_SMALL ""
    MICROPHONE_PERMISSION_ENABLED TRUE
    MICROPHONE_PERMISSION_TEXT "DAW Dan needs microphone access for audio recording"
)

# Add source files
target_sources(DAWDan PRIVATE
    src/Main.cpp
    src/MainComponent.cpp
    src/MainComponent.h
    src/AudioEngine.cpp
    src/AudioEngine.h
    src/LoopGrid.cpp
    src/LoopGrid.h
    src/MidiEngine.cpp
    src/MidiEngine.h
)

# Include directories
target_include_directories(DAWDan PRIVATE
    src
)

# Link JUCE modules
target_link_libraries(DAWDan PRIVATE
    # Core JUCE modules
    juce::juce_core
    juce::juce_events
    juce::juce_graphics
    juce::juce_data_structures

    # GUI modules
    juce::juce_gui_basics
    juce::juce_gui_extra

    # Audio modules
    juce::juce_audio_basics
    juce::juce_audio_devices
    juce::juce_audio_formats
    juce::juce_audio_processors
    juce::juce_audio_utils

    # MIDI modules
    juce::juce_midi_ci

    # Optional modules for future expansion
    juce::juce_dsp
    juce::juce_opengl
)

# Compiler definitions
target_compile_definitions(DAWDan PRIVATE
    # JUCE app config
    JUCE_WEB_BROWSER=0
    JUCE_USE_CURL=0
    JUCE_APPLICATION_NAME_STRING="$<TARGET_PROPERTY:DAWDan,JUCE_PRODUCT_NAME>"
    JUCE_APPLICATION_VERSION_STRING="$<TARGET_PROPERTY:DAWDan,JUCE_VERSION>"

    # Audio settings
    JUCE_ALSA=1
    JUCE_DIRECTSOUND=1
    JUCE_DISABLE_CAUTIOUS_PARAMETER_ID_CHECKING=1

    # MIDI settings
    JUCE_MIDI_CI=1

    # Performance settings
    JUCE_STRICT_REFCOUNTEDPOINTER=1
)

# Platform-specific settings
if(WIN32)
    target_compile_definitions(DAWDan PRIVATE
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        UNICODE
        _UNICODE
    )
endif()

# Compiler flags for optimization and warnings
target_compile_options(DAWDan PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -O3 -march=native>
    $<$<CXX_COMPILER_ID:MSVC>:/W4 /O2>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -O3 -march=native>
)

# Debug configuration
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(DAWDan PRIVATE
        DEBUG=1
        _DEBUG=1
        JUCE_DEBUG=1
    )
    target_compile_options(DAWDan PRIVATE
        $<$<CXX_COMPILER_ID:GNU>:-g -O0>
        $<$<CXX_COMPILER_ID:MSVC>:/Od /Zi>
        $<$<CXX_COMPILER_ID:Clang>:-g -O0>
    )
else()
    target_compile_definitions(DAWDan PRIVATE
        NDEBUG=1
        JUCE_DEBUG=0
    )
endif()

# Set output directory
set_target_properties(DAWDan PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)
