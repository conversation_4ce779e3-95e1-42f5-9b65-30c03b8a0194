cmake_minimum_required(VERSION 3.16)

# Project configuration
project(DAWDanGUI VERSION 1.0.0 LANGUAGES CXX)

# C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Windows-specific settings
if(WIN32)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-DNOMINMAX)
endif()

# Create GUI executable
add_executable(DAWDanGUI
    src_simple/main_gui.cpp
    src_simple/AudioEngine.cpp
    src_simple/AudioEngine.h
    src_simple/Window.cpp
    src_simple/Window.h
)

# Link libraries
target_link_libraries(DAWDanGUI
    dsound
    dxguid
    ole32
    winmm
    user32
    gdi32
    kernel32
)

# Compiler flags
target_compile_options(DAWDanGUI PRIVATE
    -Wall
    -O2
)

# Link options for Windows GUI application
target_link_options(DAWDanGUI PRIVATE
    -mwindows
)

# Define UNICODE for Windows
target_compile_definitions(DAWDanGUI PRIVATE
    UNICODE
    _UNICODE
)

# Set output directory
set_target_properties(DAWDanG<PERSON> PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/Release
)
