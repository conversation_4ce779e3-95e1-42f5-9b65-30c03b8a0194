cmake_minimum_required(VERSION 3.20)

# Project configuration
project(DAWDan VERSION 2.0.0 LANGUAGES CXX)

# Modern C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build configuration
set(CMAKE_BUILD_TYPE Release CACHE STRING "Build type")
set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "RelWithDebInfo")

# Platform-specific settings
if(WIN32)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-DNOMINMAX)
    add_definitions(-DUNICODE -D_UNICODE)
endif()

# Source files organization
set(CORE_SOURCES
    src/core/Application.cpp
    src/core/AudioEngine.cpp
    src/core/MidiEngine.cpp
    src/core/ProjectManager.cpp
)

set(AUDIO_SOURCES
    src/audio/AudioDevice.cpp
    src/audio/AudioBuffer.cpp
    src/audio/Loop.cpp
    src/audio/Track.cpp
    src/audio/Effects/Reverb.cpp
    src/audio/Effects/Delay.cpp
    src/audio/Effects/Filter.cpp
)

set(MIDI_SOURCES
    src/midi/MidiDevice.cpp
    src/midi/LaunchpadController.cpp
    src/midi/MidiMapping.cpp
)

set(INSTRUMENT_SOURCES
    src/instruments/VirtualKeyboard.cpp
    src/instruments/DrumMachine.cpp
    src/instruments/Synthesizer.cpp
    src/instruments/SamplePlayer.cpp
)

set(UI_SOURCES
    src/ui/MainWindow.cpp
    src/ui/LoopGridView.cpp
    src/ui/ArrangerView.cpp
    src/ui/InstrumentPanel.cpp
    src/ui/MixerPanel.cpp
    src/ui/SettingsPanel.cpp
)

set(UTILS_SOURCES
    src/utils/AudioUtils.cpp
    src/utils/MidiUtils.cpp
    src/utils/FileUtils.cpp
)

# Main executable
add_executable(DAWDan
    src/main.cpp
    ${CORE_SOURCES}
    ${AUDIO_SOURCES}
    ${MIDI_SOURCES}
    ${INSTRUMENT_SOURCES}
    ${UI_SOURCES}
    ${UTILS_SOURCES}
)

# Include directories
target_include_directories(DAWDan PRIVATE
    src
    src/core
    src/audio
    src/midi
    src/instruments
    src/ui
    src/utils
)

# Platform-specific libraries
if(WIN32)
    target_link_libraries(DAWDan PRIVATE
        # Audio
        dsound
        dxguid
        winmm
        # MIDI
        winmm
        # System
        ole32
        user32
        gdi32
        kernel32
        comctl32
        # Graphics
        opengl32
        glu32
    )
endif()

# Compiler flags
target_compile_options(DAWDan PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -O3 -march=native>
    $<$<CXX_COMPILER_ID:MSVC>:/W4 /O2>
)

# Link options
if(WIN32)
    target_link_options(DAWDan PRIVATE
        -mwindows
        -static-libgcc
        -static-libstdc++
    )
endif()

# Debug configuration
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(DAWDan PRIVATE DEBUG=1)
    target_compile_options(DAWDan PRIVATE
        $<$<CXX_COMPILER_ID:GNU>:-g -O0>
        $<$<CXX_COMPILER_ID:MSVC>:/Od /Zi>
    )
endif()

# Define UNICODE for Windows
target_compile_definitions(DAWDanGUI PRIVATE
    UNICODE
    _UNICODE
)

# Set output directory
set_target_properties(DAWDanGUI PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/Release
)
