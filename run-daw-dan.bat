@echo off
echo Setting up DAW Dan...

REM Copy required MinGW DLLs
copy "C:\msys64\mingw64\bin\libgcc_s_seh-1.dll" "build_ps\bin\" >nul 2>&1
copy "C:\msys64\mingw64\bin\libstdc++-6.dll" "build_ps\bin\" >nul 2>&1
copy "C:\msys64\mingw64\bin\libwinpthread-1.dll" "build_ps\bin\" >nul 2>&1

echo DLLs copied successfully!
echo Starting DAW Dan...

REM Change to the bin directory and run
cd "build_ps\bin"
"DAW Dan.exe"

pause
