#pragma once

#include <juce_audio_basics/juce_audio_basics.h>
#include <juce_audio_processors/juce_audio_processors.h>
#include <juce_gui_basics/juce_gui_basics.h>

using namespace juce;

//==============================================================================
/**
 * Drum Sample Voice - plays drum samples
 */
class DrumSampleVoice : public SynthesiserVoice
{
public:
    bool canPlaySound(SynthesiserSound* sound) override;
    void startNote(int midiNoteNumber, float velocity, SynthesiserSound* sound, int currentPitchWheelPosition) override;
    void stopNote(float velocity, bool allowTailOff) override;
    void pitchWheelMoved(int newPitchWheelValue) override {}
    void controllerMoved(int controllerNumber, int newControllerValue) override {}
    void renderNextBlock(AudioSampleBuffer& outputBuffer, int startSample, int numSamples) override;

private:
    AudioSampleBuffer* currentSample = nullptr;
    int samplePosition = 0;
    float level = 0.0f;
    bool isPlaying = false;
};

//==============================================================================
/**
 * Drum Sample Sound - holds drum sample data
 */
class DrumSampleSound : public SynthesiserSound
{
public:
    DrumSampleSound(const String& name, AudioSampleBuffer& source, int midiNote);
    
    bool appliesToNote(int midiNoteNumber) override { return midiNoteNumber == midiNoteNumber_; }
    bool appliesToChannel(int midiChannel) override { return true; }
    
    AudioSampleBuffer* getAudioData() { return &data; }
    String getName() const { return name_; }

private:
    String name_;
    AudioSampleBuffer data;
    int midiNoteNumber_;
};

//==============================================================================
/**
 * 16-Pad Drum Machine Component
 */
class DrumMachine : public Component,
                    public Button::Listener
{
public:
    //==============================================================================
    DrumMachine();
    ~DrumMachine() override;

    //==============================================================================
    void paint(Graphics& g) override;
    void resized() override;
    void buttonClicked(Button* button) override;

    //==============================================================================
    // Audio processing
    void prepareToPlay(double sampleRate, int blockSize);
    void getNextAudioBlock(AudioSampleBuffer& buffer);
    void releaseResources();

    //==============================================================================
    // MIDI handling
    void processMidiMessage(const MidiMessage& message);
    void triggerPad(int padIndex, float velocity = 1.0f);

private:
    //==============================================================================
    // Drum pads
    static constexpr int numPads = 16;
    std::array<std::unique_ptr<TextButton>, numPads> drumPads;
    std::array<String, numPads> padNames = {
        "Kick", "Snare", "HiHat", "OpenHat",
        "Crash", "Ride", "Tom1", "Tom2",
        "Tom3", "Clap", "Perc1", "Perc2",
        "Perc3", "Perc4", "Perc5", "Perc6"
    };

    //==============================================================================
    // Synthesiser for drum sounds
    Synthesiser drumSynth;
    
    //==============================================================================
    // Sample generation
    void createDrumSamples();
    AudioSampleBuffer generateKickSample(double sampleRate);
    AudioSampleBuffer generateSnareSample(double sampleRate);
    AudioSampleBuffer generateHiHatSample(double sampleRate);
    AudioSampleBuffer generateClapSample(double sampleRate);

    //==============================================================================
    double currentSampleRate = 44100.0;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(DrumMachine)
};
