@echo off
echo Quick DAW Dan build (minimal JUCE)...

REM Add MSYS2 MinGW and CMake paths
set PATH=C:\msys64\mingw64\bin;C:\msys64\usr\bin;%PATH%

REM Clean previous build
if exist build rmdir /s /q build
mkdir build
cd build

REM Configure with minimal options
echo Configuring with minimal JUCE build...
cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release -DJUCE_BUILD_EXAMPLES=OFF -DJUCE_BUILD_EXTRAS=OFF ..

if %errorlevel% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build with single thread to avoid memory issues
echo Building (this may take 10-15 minutes)...
mingw32-make -j1

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build complete! DAWDan.exe should be ready.
pause
