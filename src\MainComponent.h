#pragma once

#include <JuceHeader.h>
#include "AudioEngine.h"
#include "LoopGrid.h"

//==============================================================================
// Sprint Version - Simplified MainComponent
class MainComponent : public juce::AudioAppComponent,
                     public juce::Button::Listener,
                     public juce::Timer
{
public:
    //==============================================================================
    MainComponent();
    ~MainComponent() override;

    //==============================================================================
    void prepareToPlay (int samplesPerBlockExpected, double sampleRate) override;
    void getNextAudioBlock (const juce::AudioSourceChannelInfo& bufferToFill) override;
    void releaseResources() override;

    //==============================================================================
    void paint (juce::Graphics& g) override;
    void resized() override;
    void buttonClicked (juce::Button* button) override;
    void timerCallback() override;

private:
    //==============================================================================
    // Core components
    std::unique_ptr<AudioEngine> audioEngine;
    std::unique_ptr<LoopGrid> loopGrid;

    //==============================================================================
    // Transport controls
    juce::TextButton playButton;
    juce::TextButton stopButton;
    juce::TextButton recordButton;

    //==============================================================================
    // Status display
    juce::Label statusLabel;
    juce::Label bpmLabel;

    //==============================================================================
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (MainComponent)
};
