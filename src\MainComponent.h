#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_audio_utils/juce_audio_utils.h>
#include <juce_audio_devices/juce_audio_devices.h>
#include "AudioEngine.h"
#include "MidiEngine.h"
#include "LoopGrid.h"

using namespace juce;

//==============================================================================
// Sprint Version - Simplified MainComponent
class MainComponent : public AudioAppComponent,
                     public Button::Listener,
                     public Timer
{
public:
    //==============================================================================
    MainComponent();
    ~MainComponent() override;

    //==============================================================================
    void prepareToPlay (int samplesPerBlockExpected, double sampleRate) override;
    void getNextAudioBlock (const AudioSourceChannelInfo& bufferToFill) override;
    void releaseResources() override;

    //==============================================================================
    void paint (Graphics& g) override;
    void resized() override;
    void buttonClicked (Button* button) override;
    void timerCallback() override;

private:
    //==============================================================================
    // Core components
    std::unique_ptr<AudioEngine> audioEngine;
    std::unique_ptr<MidiEngine> midiEngine;
    std::unique_ptr<LoopGrid> loopGrid;

    //==============================================================================
    // Transport controls
    TextButton playButton;
    TextButton stopButton;
    TextButton recordButton;

    //==============================================================================
    // Status display
    Label statusLabel;
    Label bpmLabel;

    //==============================================================================
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (MainComponent)
};
