#include "Window.h"
#include <iostream>

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    // Enable console for debugging (optional)
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    
    std::cout << "Starting DAW Dan GUI..." << std::endl;
    
    try {
        Window window(hInstance);
        
        if (!window.Create(L"DAW Dan - Live Looping Studio", 1000, 700)) {
            std::cerr << "Failed to create window!" << std::endl;
            return -1;
        }
        
        window.Show(nCmdShow);
        
        // Message loop
        MSG msg = {};
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        return static_cast<int>(msg.wParam);
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        MessageBoxA(nullptr, e.what(), "DAW Dan Error", MB_OK | MB_ICONERROR);
        return -1;
    }
}
