#include <iostream>
#include <windows.h>
#include <dsound.h>

int main() {
    std::cout << "Testing DirectSound..." << std::endl;
    
    // Initialize COM
    CoInitialize(nullptr);
    
    // Try to create DirectSound
    LPDIRECTSOUND8 pDirectSound = nullptr;
    HRESULT hr = DirectSoundCreate8(nullptr, &pDirectSound, nullptr);
    
    if (SUCCEEDED(hr)) {
        std::cout << "DirectSound created successfully!" << std::endl;
        
        // Set cooperative level
        hr = pDirectSound->SetCooperativeLevel(GetDesktopWindow(), DSSCL_PRIORITY);
        if (SUCCEEDED(hr)) {
            std::cout << "Cooperative level set successfully!" << std::endl;
        }
        
        pDirectSound->Release();
        std::cout << "DirectSound test completed successfully!" << std::endl;
    } else {
        std::cout << "Failed to create DirectSound! Error: 0x" << std::hex << hr << std::endl;
    }
    
    CoUninitialize();
    
    std::cout << "Press Enter to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}
