#pragma once

#include <windows.h>
#include <mmsystem.h>  // Must come before dsound.h
#include <dsound.h>
#include <vector>
#include <memory>
#include <atomic>
#include <thread>

class AudioEngine {
public:
    AudioEngine();
    ~AudioEngine();
    
    bool Initialize();
    void Shutdown();
    
    // Transport controls
    void Play();
    void Stop();
    void StartRecording();
    void StopRecording();
    
    // Loop management
    void TriggerLoop(int index);
    void StopLoop(int index);
    bool IsLoopPlaying(int index) const;
    bool HasLoopContent(int index) const;
    
    // State queries
    bool IsPlaying() const { return m_isPlaying.load(); }
    bool IsRecording() const { return m_isRecording.load(); }
    double GetBPM() const { return 120.0; }  // Fixed for simplicity
    
private:
    struct Loop {
        std::vector<short> audioData;  // 16-bit PCM data
        bool isPlaying = false;
        bool hasContent = false;
        size_t playPosition = 0;
        float volume = 1.0f;
    };
    
    // DirectSound objects
    LPDIRECTSOUND8 m_pDirectSound;
    LPDIRECTSOUNDBUFFER m_pPrimaryBuffer;
    LPDIRECTSOUNDBUFFER m_pSecondaryBuffer;
    LPDIRECTSOUNDCAPTURE8 m_pDirectSoundCapture;
    LPDIRECTSOUNDCAPTUREBUFFER8 m_pCaptureBuffer;
    
    // Audio format
    static const int SAMPLE_RATE = 44100;
    static const int CHANNELS = 2;
    static const int BITS_PER_SAMPLE = 16;
    static const int BUFFER_SIZE = 4096;
    static const int MAX_RECORD_SAMPLES = SAMPLE_RATE * 4;  // 4 seconds max
    
    // Audio thread
    std::thread m_audioThread;
    std::atomic<bool> m_shouldStop{false};
    void AudioThreadProc();
    
    // State
    std::atomic<bool> m_isPlaying{false};
    std::atomic<bool> m_isRecording{false};
    
    // Loops (16 total for 4x4 grid)
    static const int NUM_LOOPS = 16;
    Loop m_loops[NUM_LOOPS];
    
    // Recording
    std::vector<short> m_recordBuffer;
    size_t m_recordPosition = 0;
    
    // Audio processing
    void ProcessAudio(short* outputBuffer, int numSamples);
    void ProcessRecording();
    void MixLoops(short* outputBuffer, int numSamples);
    
    // DirectSound helpers
    bool CreateDirectSound();
    bool CreateCaptureDevice();
    bool CreateBuffers();
    WAVEFORMATEX GetWaveFormat();

    // Utility functions
    void ClearLoop(int index);
    int FindEmptyLoop();
};
