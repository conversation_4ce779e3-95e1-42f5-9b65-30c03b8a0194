#pragma once

#include <windows.h>
#include <memory>
#include <vector>
#include "AudioEngine.h"

class Window {
public:
    Window(HINSTANCE hInstance);
    ~Window();

    bool Create(const wchar_t* title, int width, int height);
    void Show(int nCmdShow);

private:
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam);

    void OnPaint(HDC hdc);
    void OnCommand(WPARAM wParam);
    void OnLButtonDown(int x, int y);
    void OnKeyDown(WPARAM key);
    void OnTimer();

    void CreateControls();
    void UpdateDisplay();
    void DrawLoopGrid(HDC hdc);
    void DrawTransportControls(HDC hdc);
    void DrawStatusBar(HDC hdc);

    HINSTANCE m_hInstance;
    HWND m_hwnd;
    
    // Audio engine
    std::unique_ptr<AudioEngine> m_audioEngine;

    // GUI state
    bool m_isDragging = false;
    int m_selectedLoop = -1;

    // Loop grid layout
    static const int GRID_SIZE = 4;
    static const int GRID_COLS = 4;
    static const int GRID_ROWS = 4;

    struct LoopButton {
        RECT rect;
        bool isHovered = false;
        bool isPressed = false;
    };

    std::vector<LoopButton> m_loopButtons;
    
    // Transport button areas
    RECT m_playButtonRect;
    RECT m_stopButtonRect;
    RECT m_recordButtonRect;

    // Colors and brushes
    HBRUSH m_backgroundBrush;
    HBRUSH m_gridBrush;
    HBRUSH m_buttonBrush;
    HBRUSH m_playingBrush;
    HBRUSH m_recordingBrush;
    HPEN m_borderPen;

    // Fonts
    HFONT m_titleFont;
    HFONT m_buttonFont;
    HFONT m_statusFont;

    // Timer for UI updates
    static const UINT_PTR TIMER_ID = 1;

    // Control IDs (for legacy OnCommand method)
    enum {
        ID_PLAY = 1001,
        ID_STOP = 1002,
        ID_RECORD = 1003,
        ID_LOOP_START = 2000  // Loop buttons start from 2000
    };

    // Helper functions
    int GetLoopIndexFromPoint(int x, int y);
    RECT GetLoopButtonRect(int index);
    COLORREF GetLoopColor(int index);
    void InvalidateLoopButton(int index);
};
