#pragma once

#include <JuceHeader.h>
#include <memory>
#include <vector>
#include <atomic>

class Loop;
class LoopRecorder;
class LoopPlayer;

//==============================================================================
/**
 * Core audio engine for DAW Dan
 * Handles audio I/O, transport control, and loop management
 */
class AudioEngine : public juce::AudioSource,
                   public juce::Timer
{
public:
    //==============================================================================
    AudioEngine();
    ~AudioEngine() override;

    //==============================================================================
    // AudioSource interface
    void prepareToPlay (int samplesPerBlockExpected, double sampleRate) override;
    void getNextAudioBlock (const juce::AudioSourceChannelInfo& bufferToFill) override;
    void releaseResources() override;

    //==============================================================================
    // Transport controls
    void play();
    void stop();
    void pause();
    void record();
    bool isPlaying() const { return isTransportPlaying.load(); }
    bool isRecording() const { return isTransportRecording.load(); }
    bool isPaused() const { return isTransportPaused.load(); }

    //==============================================================================
    // Tempo and timing
    void setBPM(double newBPM);
    double getBPM() const { return currentBPM.load(); }
    double getCurrentTimeInBeats() const;
    double getCurrentTimeInSamples() const;
    int getCurrentBar() const;
    int getCurrentBeat() const;

    //==============================================================================
    // Loop management
    int createLoop(int trackIndex);
    void deleteLoop(int loopId);
    void triggerLoop(int loopId);
    void stopLoop(int loopId);
    void muteLoop(int loopId, bool shouldMute);
    void setLoopVolume(int loopId, float volume);
    
    //==============================================================================
    // Recording
    void startRecording(int trackIndex);
    void stopRecording();
    int getRecordingTrack() const { return currentRecordingTrack.load(); }

    //==============================================================================
    // Track management
    void muteTrack(int trackIndex, bool shouldMute);
    void setTrackVolume(int trackIndex, float volume);
    void soloTrack(int trackIndex, bool shouldSolo);
    
    //==============================================================================
    // Audio settings
    void setMasterVolume(float volume);
    float getMasterVolume() const { return masterVolume.load(); }
    bool isInitialized() const { return initialized.load(); }
    
    //==============================================================================
    // Quantization
    enum class QuantizeMode
    {
        Off,
        Bar,
        Beat,
        HalfBeat,
        QuarterBeat
    };
    
    void setQuantizeMode(QuantizeMode mode) { quantizeMode = mode; }
    QuantizeMode getQuantizeMode() const { return quantizeMode; }

    //==============================================================================
    // Metronome
    void setMetronomeEnabled(bool enabled) { metronomeEnabled.store(enabled); }
    bool isMetronomeEnabled() const { return metronomeEnabled.load(); }
    void setMetronomeVolume(float volume) { metronomeVolume.store(volume); }

    //==============================================================================
    // Timer callback for UI updates
    void timerCallback() override;

    //==============================================================================
    // Listeners for state changes
    class Listener
    {
    public:
        virtual ~Listener() = default;
        virtual void transportStateChanged() {}
        virtual void tempoChanged(double newBPM) {}
        virtual void loopStateChanged(int loopId) {}
        virtual void recordingStateChanged() {}
    };
    
    void addListener(Listener* listener);
    void removeListener(Listener* listener);

private:
    //==============================================================================
    // Audio parameters
    std::atomic<double> sampleRate { 44100.0 };
    std::atomic<int> blockSize { 512 };
    std::atomic<bool> initialized { false };

    //==============================================================================
    // Transport state
    std::atomic<bool> isTransportPlaying { false };
    std::atomic<bool> isTransportRecording { false };
    std::atomic<bool> isTransportPaused { false };
    std::atomic<double> currentBPM { 120.0 };
    std::atomic<double> currentTimeInSamples { 0.0 };
    
    //==============================================================================
    // Recording state
    std::atomic<int> currentRecordingTrack { -1 };
    std::unique_ptr<LoopRecorder> loopRecorder;
    
    //==============================================================================
    // Audio components
    std::atomic<float> masterVolume { 0.75f };
    std::atomic<bool> metronomeEnabled { false };
    std::atomic<float> metronomeVolume { 0.5f };
    
    //==============================================================================
    // Loops and tracks
    static constexpr int MAX_TRACKS = 8;
    static constexpr int MAX_LOOPS_PER_TRACK = 8;
    
    std::vector<std::unique_ptr<Loop>> loops;
    std::vector<std::unique_ptr<LoopPlayer>> loopPlayers;
    std::array<float, MAX_TRACKS> trackVolumes;
    std::array<bool, MAX_TRACKS> trackMuted;
    std::array<bool, MAX_TRACKS> trackSoloed;
    
    //==============================================================================
    // Quantization
    QuantizeMode quantizeMode = QuantizeMode::Beat;
    double getNextQuantizedTime() const;
    bool shouldQuantizeNow() const;
    
    //==============================================================================
    // Metronome
    juce::AudioBuffer<float> metronomeBuffer;
    int metronomePosition = 0;
    void generateMetronomeClick();
    void processMetronome(juce::AudioBuffer<float>& buffer, int startSample, int numSamples);
    
    //==============================================================================
    // Audio processing
    void processTransport(int numSamples);
    void processLoops(juce::AudioBuffer<float>& buffer);
    void mixTracks(juce::AudioBuffer<float>& buffer);
    
    //==============================================================================
    // Listeners
    juce::ListenerList<Listener> listeners;
    void notifyTransportStateChanged();
    void notifyTempoChanged();
    void notifyLoopStateChanged(int loopId);
    void notifyRecordingStateChanged();
    
    //==============================================================================
    // Thread safety
    juce::CriticalSection audioLock;
    
    //==============================================================================
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (AudioEngine)
};
