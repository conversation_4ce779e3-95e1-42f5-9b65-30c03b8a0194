#include "AudioEngine.h"
#include <iostream>
#include <string>
#include <thread>
#include <chrono>

int main() {
    std::cout << "=== DAW Dan Simple - Console Version ===" << std::endl;
    std::cout << "Live Looping Audio Engine Test" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // Create audio engine
    AudioEngine engine;
    
    if (!engine.Initialize()) {
        std::cerr << "Failed to initialize audio engine!" << std::endl;
        return -1;
    }
    
    std::cout << "\nAudio engine initialized successfully!" << std::endl;
    std::cout << "\nCommands:" << std::endl;
    std::cout << "  p - Play/Stop transport" << std::endl;
    std::cout << "  r - Start/Stop recording" << std::endl;
    std::cout << "  1-9 - Trigger loops 1-9" << std::endl;
    std::cout << "  0 - Trigger loop 10" << std::endl;
    std::cout << "  s - Show status" << std::endl;
    std::cout << "  q - Quit" << std::endl;
    std::cout << "\nReady! Type commands and press Enter:" << std::endl;
    
    std::string input;
    bool running = true;
    
    while (running) {
        std::cout << "> ";
        std::getline(std::cin, input);
        
        if (input.empty()) continue;
        
        char command = input[0];
        
        switch (command) {
            case 'p':
            case 'P':
                if (engine.IsPlaying()) {
                    engine.Stop();
                    std::cout << "Transport: STOPPED" << std::endl;
                } else {
                    engine.Play();
                    std::cout << "Transport: PLAYING" << std::endl;
                }
                break;
                
            case 'r':
            case 'R':
                if (engine.IsRecording()) {
                    engine.StopRecording();
                    std::cout << "Recording: STOPPED" << std::endl;
                } else {
                    engine.StartRecording();
                    std::cout << "Recording: STARTED (speak into microphone)" << std::endl;
                }
                break;
                
            case '1': case '2': case '3': case '4': case '5':
            case '6': case '7': case '8': case '9':
                {
                    int loopIndex = command - '1';
                    engine.TriggerLoop(loopIndex);
                    if (engine.HasLoopContent(loopIndex)) {
                        std::cout << "Loop " << (loopIndex + 1) << ": " 
                                  << (engine.IsLoopPlaying(loopIndex) ? "PLAYING" : "STOPPED") 
                                  << std::endl;
                    } else {
                        std::cout << "Loop " << (loopIndex + 1) << ": EMPTY (record something first)" << std::endl;
                    }
                }
                break;
                
            case '0':
                {
                    int loopIndex = 9;
                    engine.TriggerLoop(loopIndex);
                    if (engine.HasLoopContent(loopIndex)) {
                        std::cout << "Loop 10: " 
                                  << (engine.IsLoopPlaying(loopIndex) ? "PLAYING" : "STOPPED") 
                                  << std::endl;
                    } else {
                        std::cout << "Loop 10: EMPTY (record something first)" << std::endl;
                    }
                }
                break;
                
            case 's':
            case 'S':
                {
                    std::cout << "\n=== STATUS ===" << std::endl;
                    std::cout << "Transport: " << (engine.IsPlaying() ? "PLAYING" : "STOPPED") << std::endl;
                    std::cout << "Recording: " << (engine.IsRecording() ? "ACTIVE" : "INACTIVE") << std::endl;
                    std::cout << "BPM: " << engine.GetBPM() << std::endl;
                    
                    std::cout << "\nLoops:" << std::endl;
                    for (int i = 0; i < 16; i++) {
                        if (engine.HasLoopContent(i)) {
                            std::cout << "  Loop " << (i + 1) << ": " 
                                      << (engine.IsLoopPlaying(i) ? "PLAYING" : "STOPPED") 
                                      << std::endl;
                        }
                    }
                    std::cout << "==============" << std::endl;
                }
                break;
                
            case 'q':
            case 'Q':
                running = false;
                std::cout << "Shutting down..." << std::endl;
                break;
                
            case 'h':
            case 'H':
            case '?':
                std::cout << "\nCommands:" << std::endl;
                std::cout << "  p - Play/Stop transport" << std::endl;
                std::cout << "  r - Start/Stop recording" << std::endl;
                std::cout << "  1-9 - Trigger loops 1-9" << std::endl;
                std::cout << "  0 - Trigger loop 10" << std::endl;
                std::cout << "  s - Show status" << std::endl;
                std::cout << "  q - Quit" << std::endl;
                break;
                
            default:
                std::cout << "Unknown command. Type 'h' for help." << std::endl;
                break;
        }
    }
    
    engine.Shutdown();
    std::cout << "DAW Dan Simple shutdown complete." << std::endl;
    
    return 0;
}
