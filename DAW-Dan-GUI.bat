@echo off
title DAW Dan - Live Looping Studio
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    DAW DAN - LIVE LOOPING STUDIO             ║
echo  ║                         GUI Launcher                         ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.
echo  🎵 Welcome to DAW Dan - Your Live Looping Studio! 🎵
echo.
echo  ┌─ FEATURES ─────────────────────────────────────────────────┐
echo  │ • Real-time audio recording and playback                   │
echo  │ • 16 independent loop slots (4x4 grid)                     │
echo  │ • DirectSound audio engine                                 │
echo  │ • Live looping with instant triggering                     │
echo  │ • Professional 44.1kHz audio quality                       │
echo  └─────────────────────────────────────────────────────────────┘
echo.
echo  ┌─ CONTROLS ─────────────────────────────────────────────────┐
echo  │ • P - Play/Stop transport                                  │
echo  │ • R - Start/Stop recording                                 │
echo  │ • 1-9, 0 - Trigger loops 1-10                             │
echo  │ • S - Show status                                          │
echo  │ • Q - Quit                                                 │
echo  └─────────────────────────────────────────────────────────────┘
echo.
echo  ┌─ QUICK START ──────────────────────────────────────────────┐
echo  │ 1. Press R to start recording                              │
echo  │ 2. Make some sound (speak, sing, clap)                     │
echo  │ 3. Press R again to stop recording                         │
echo  │ 4. Press 1 to trigger your first loop                      │
echo  │ 5. Press P to start/stop playback                          │
echo  └─────────────────────────────────────────────────────────────┘
echo.

:menu
echo  ┌─ LAUNCH OPTIONS ───────────────────────────────────────────┐
echo  │ [1] Launch DAW Dan Console                                 │
echo  │ [2] View System Status                                     │
echo  │ [3] Exit                                                   │
echo  └─────────────────────────────────────────────────────────────┘
echo.
set /p choice="  Enter your choice (1-3): "

if "%choice%"=="1" goto launch
if "%choice%"=="2" goto status
if "%choice%"=="3" goto exit
echo  Invalid choice. Please try again.
echo.
goto menu

:launch
echo.
echo  🚀 Launching DAW Dan Console...
echo.
if exist "build_vs\Release\DAWDanSimple.exe" (
    echo  ✅ Found DAWDanSimple.exe
    echo  ✅ Starting audio engine...
    echo.
    start "DAW Dan Console" "build_vs\Release\DAWDanSimple.exe"
    echo  🎵 DAW Dan Console is now running in a separate window!
    echo.
    echo  You can now:
    echo  • Record loops by pressing R
    echo  • Trigger loops with number keys 1-9, 0
    echo  • Control playback with P (play/stop)
    echo.
    pause
) else (
    echo  ❌ Error: DAWDanSimple.exe not found!
    echo.
    echo  Please build the console version first:
    echo  1. Run: build-vs.bat
    echo  2. Wait for compilation to complete
    echo  3. Try launching again
    echo.
    pause
)
goto menu

:status
echo.
echo  ┌─ SYSTEM STATUS ────────────────────────────────────────────┐
if exist "build_vs\Release\DAWDanSimple.exe" (
    echo  │ ✅ DAW Dan Console: READY                                  │
) else (
    echo  │ ❌ DAW Dan Console: NOT BUILT                              │
)

if exist "src_simple\AudioEngine.cpp" (
    echo  │ ✅ Audio Engine: AVAILABLE                                 │
) else (
    echo  │ ❌ Audio Engine: MISSING                                   │
)

if exist "build_vs" (
    echo  │ ✅ Build Directory: EXISTS                                 │
) else (
    echo  │ ❌ Build Directory: MISSING                                │
)
echo  │                                                            │
echo  │ Audio System: DirectSound (Windows)                       │
echo  │ Sample Rate: 44.1 kHz                                     │
echo  │ Bit Depth: 16-bit                                         │
echo  │ Channels: Stereo                                          │
echo  │ Loop Slots: 16 (4x4 grid)                                 │
echo  └─────────────────────────────────────────────────────────────┘
echo.
pause
goto menu

:exit
echo.
echo  👋 Thanks for using DAW Dan!
echo  🎵 Keep making music! 🎵
echo.
pause
exit
