#pragma once

#include <windows.h>

class LoopGrid {
public:
    LoopGrid();
    ~LoopGrid();
    
    // Grid management
    void SetButtonState(int index, bool hasContent, bool isPlaying);
    void UpdateDisplay();
    
private:
    static const int GRID_SIZE = 4;
    static const int NUM_BUTTONS = GRID_SIZE * GRID_SIZE;
    
    struct ButtonState {
        bool hasContent = false;
        bool isPlaying = false;
    };
    
    ButtonState m_buttonStates[NUM_BUTTONS];
};
