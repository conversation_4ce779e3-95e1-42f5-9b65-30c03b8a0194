#include "VirtualPiano.h"

using namespace juce;

//==============================================================================
VirtualPiano::VirtualPiano()
{
    // Add voices to the synthesiser
    for (int i = 0; i < 4; ++i)
        synth.addVoice(new SineWaveVoice());

    synth.addSound(new SineWaveSound());

    // Create the keyboard component
    keyboardComponent = std::make_unique<MidiKeyboardComponent>(keyboardState, MidiKeyboardComponent::horizontalKeyboard);
    addAndMakeVisible(*keyboardComponent);

    // Listen to keyboard state changes
    keyboardState.addListener(this);

    // Set keyboard range (88 keys: A0 to C8)
    keyboardComponent->setKeyWidth(12.0f);
    keyboardComponent->setLowestVisibleKey(21); // A0
}

VirtualPiano::~VirtualPiano()
{
    keyboardState.removeListener(this);
}

//==============================================================================
void VirtualPiano::paint(Graphics& g)
{
    // Dark background
    g.fillAll(Colour(0xff2a2a2a));

    // Title
    g.setColour(Colours::white);
    g.setFont(Font(20.0f, Font::bold));
    g.drawText("Virtual Piano", getLocalBounds().removeFromTop(40), Justification::centred, true);
}

void VirtualPiano::resized()
{
    auto area = getLocalBounds();
    
    // Leave space for title
    area.removeFromTop(40);
    
    // Keyboard takes the rest
    keyboardComponent->setBounds(area);
}

//==============================================================================
void VirtualPiano::prepareToPlay(double sampleRate, int blockSize)
{
    currentSampleRate = sampleRate;
    synth.setCurrentPlaybackSampleRate(sampleRate);
    
    DBG("VirtualPiano prepared: " << sampleRate << "Hz, " << blockSize << " samples");
}

void VirtualPiano::getNextAudioBlock(AudioSampleBuffer& buffer)
{
    // Clear the buffer
    buffer.clear();
    
    // Create a MIDI buffer (empty for now, could be used for external MIDI)
    MidiBuffer midiMessages;
    
    // Render the synthesiser
    synth.renderNextBlock(buffer, midiMessages, 0, buffer.getNumSamples());
}

void VirtualPiano::releaseResources()
{
    DBG("VirtualPiano resources released");
}

//==============================================================================
void VirtualPiano::handleNoteOn(MidiKeyboardState*, int midiChannel, int midiNoteNumber, float velocity)
{
    // Play the note on the synthesiser
    synth.noteOn(midiChannel, midiNoteNumber, velocity);
    DBG("Piano Note On: " << midiNoteNumber << " velocity: " << velocity);
}

void VirtualPiano::handleNoteOff(MidiKeyboardState*, int midiChannel, int midiNoteNumber, float velocity)
{
    // Stop the note on the synthesiser
    synth.noteOff(midiChannel, midiNoteNumber, velocity, true);
    DBG("Piano Note Off: " << midiNoteNumber);
}

//==============================================================================
void VirtualPiano::processMidiMessage(const MidiMessage& message)
{
    // Handle external MIDI input
    if (message.isNoteOn())
    {
        keyboardState.noteOn(message.getChannel(), message.getNoteNumber(), message.getFloatVelocity());
    }
    else if (message.isNoteOff())
    {
        keyboardState.noteOff(message.getChannel(), message.getNoteNumber(), message.getFloatVelocity());
    }
}
