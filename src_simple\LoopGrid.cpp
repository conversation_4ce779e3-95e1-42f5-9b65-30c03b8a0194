#include "LoopGrid.h"

LoopGrid::LoopGrid() {
    // Initialize all button states
    for (int i = 0; i < NUM_BUTTONS; i++) {
        m_buttonStates[i] = {};
    }
}

LoopGrid::~LoopGrid() {
    // Nothing to clean up
}

void LoopGrid::SetButtonState(int index, bool hasContent, bool isPlaying) {
    if (index >= 0 && index < NUM_BUTTONS) {
        m_buttonStates[index].hasContent = hasContent;
        m_buttonStates[index].isPlaying = isPlaying;
    }
}

void LoopGrid::UpdateDisplay() {
    // This would update visual feedback if we had more complex graphics
    // For now, the Window class handles the button text updates
}
