#include "Timeline.h"

using namespace juce;

//==============================================================================
Timeline::Timeline()
{
    // Add a few default tracks
    addTrack("Track 1");
    addTrack("Track 2");
    addTrack("Track 3");
    addTrack("Track 4");
}

Timeline::~Timeline() = default;

//==============================================================================
void Timeline::paint(Graphics& g)
{
    auto area = getLocalBounds();
    
    // Background
    g.fillAll(Colour(0xff1a1a1a));
    
    // Split into header and timeline areas
    auto headerArea = area.removeFromLeft(headerWidth);
    auto timelineArea = area;
    
    // Draw track headers
    g.setColour(Colour(0xff2a2a2a));
    g.fillRect(headerArea);
    
    for (int i = 0; i < (int)tracks.size(); ++i)
    {
        auto trackHeaderBounds = headerArea.removeFromTop(trackHeight);
        
        g.setColour(Colour(0xff3a3a3a));
        g.drawRect(trackHeaderBounds, 1);
        
        g.setColour(Colours::white);
        g.setFont(14.0f);
        g.drawText(tracks[i].name, trackHeaderBounds.reduced(5), Justification::centredLeft);
    }
    
    // Draw timeline
    drawTimeRuler(g, timelineArea.removeFromTop(30));
    drawTracks(g, timelineArea);
    drawPlayhead(g, getLocalBounds().removeFromLeft(headerWidth).removeFromRight(getWidth() - headerWidth));
}

void Timeline::resized()
{
    // Timeline resizing is handled in paint
}

//==============================================================================
void Timeline::mouseDown(const MouseEvent& e)
{
    auto timelineArea = getLocalBounds().removeFromLeft(headerWidth).removeFromRight(getWidth() - headerWidth);
    
    if (timelineArea.contains(e.getPosition()))
    {
        double clickTime = pixelToTime(e.x - headerWidth);
        
        // Check if clicking on playhead
        int playheadX = timeToPixel(playheadPosition) + headerWidth;
        if (std::abs(e.x - playheadX) < 5)
        {
            draggingPlayhead = true;
            return;
        }
        
        // Check if clicking on a clip
        for (int trackIndex = 0; trackIndex < (int)tracks.size(); ++trackIndex)
        {
            auto& track = tracks[trackIndex];
            for (int clipIndex = 0; clipIndex < (int)track.clips.size(); ++clipIndex)
            {
                auto clipBounds = getClipBounds(track.clips[clipIndex], trackIndex);
                if (clipBounds.contains(e.getPosition()))
                {
                    selectClip(trackIndex, clipIndex);
                    draggingClip = true;
                    draggedClip = &track.clips[clipIndex];
                    dragStartPosition = e.getPosition();
                    return;
                }
            }
        }
        
        // Otherwise, move playhead
        setPlayheadPosition(clickTime);
        if (onPlayheadMoved)
            onPlayheadMoved(playheadPosition);
    }
}

void Timeline::mouseDrag(const MouseEvent& e)
{
    if (draggingPlayhead)
    {
        double newTime = pixelToTime(e.x - headerWidth);
        setPlayheadPosition(jmax(0.0, newTime));
        if (onPlayheadMoved)
            onPlayheadMoved(playheadPosition);
        repaint();
    }
    else if (draggingClip && draggedClip)
    {
        // Move the clip
        double newStartTime = pixelToTime(e.x - headerWidth);
        draggedClip->startTime = jmax(0.0, newStartTime);
        repaint();
    }
}

void Timeline::mouseUp(const MouseEvent& e)
{
    if (draggingClip && draggedClip && onClipMoved)
    {
        onClipMoved(selectedTrack, *draggedClip);
    }
    
    draggingPlayhead = false;
    draggingClip = false;
    draggedClip = nullptr;
}

//==============================================================================
bool Timeline::isInterestedInDragSource(const SourceDetails& dragSourceDetails)
{
    // Accept drops from loop grid
    return dragSourceDetails.description.toString().startsWith("loop:");
}

void Timeline::itemDropped(const SourceDetails& dragSourceDetails)
{
    String description = dragSourceDetails.description.toString();
    if (description.startsWith("loop:"))
    {
        int loopIndex = description.substring(5).getIntValue();
        
        // Calculate drop position
        double dropTime = pixelToTime(dragSourceDetails.localPosition.x - headerWidth);
        int trackIndex = (dragSourceDetails.localPosition.y - 30) / trackHeight; // Account for ruler
        
        if (trackIndex >= 0 && trackIndex < (int)tracks.size())
        {
            addClip(trackIndex, dropTime, 4.0, loopIndex); // 4 second default duration
            repaint();
        }
    }
}

//==============================================================================
void Timeline::setPlayheadPosition(double timeInSeconds)
{
    playheadPosition = jmax(0.0, timeInSeconds);
    repaint();
}

void Timeline::setZoom(double pixelsPerSecond)
{
    zoom = jmax(10.0, jmin(200.0, pixelsPerSecond));
    repaint();
}

void Timeline::setViewStart(double timeInSeconds)
{
    viewStart = jmax(0.0, timeInSeconds);
    repaint();
}

//==============================================================================
void Timeline::addTrack(const String& name)
{
    ArrangerTrack newTrack;
    newTrack.name = name;
    newTrack.colour = Colour::fromHSV(tracks.size() * 0.1f, 0.6f, 0.8f, 1.0f);
    tracks.push_back(newTrack);
    repaint();
}

void Timeline::removeTrack(int trackIndex)
{
    if (trackIndex >= 0 && trackIndex < (int)tracks.size())
    {
        tracks.erase(tracks.begin() + trackIndex);
        repaint();
    }
}

void Timeline::addClip(int trackIndex, double startTime, double duration, int loopIndex)
{
    if (trackIndex >= 0 && trackIndex < (int)tracks.size())
    {
        TimelineClip clip;
        clip.trackIndex = trackIndex;
        clip.startTime = startTime;
        clip.duration = duration;
        clip.loopIndex = loopIndex;
        clip.name = "Loop " + String(loopIndex + 1);
        clip.colour = tracks[trackIndex].colour;
        
        tracks[trackIndex].clips.push_back(clip);
        repaint();
    }
}

void Timeline::removeClip(int trackIndex, int clipIndex)
{
    if (trackIndex >= 0 && trackIndex < (int)tracks.size())
    {
        auto& clips = tracks[trackIndex].clips;
        if (clipIndex >= 0 && clipIndex < (int)clips.size())
        {
            clips.erase(clips.begin() + clipIndex);
            repaint();
        }
    }
}

void Timeline::selectClip(int trackIndex, int clipIndex)
{
    clearSelection();
    
    if (trackIndex >= 0 && trackIndex < (int)tracks.size())
    {
        auto& clips = tracks[trackIndex].clips;
        if (clipIndex >= 0 && clipIndex < (int)clips.size())
        {
            selectedTrack = trackIndex;
            selectedClip = clipIndex;
            clips[clipIndex].selected = true;
            
            if (onClipSelected)
                onClipSelected(trackIndex, clipIndex);
            
            repaint();
        }
    }
}

void Timeline::clearSelection()
{
    for (auto& track : tracks)
    {
        for (auto& clip : track.clips)
        {
            clip.selected = false;
        }
    }
    
    selectedTrack = -1;
    selectedClip = -1;
}

//==============================================================================
double Timeline::pixelToTime(int pixel) const
{
    return viewStart + (pixel / zoom);
}

int Timeline::timeToPixel(double time) const
{
    return (int)((time - viewStart) * zoom);
}

Rectangle<int> Timeline::getClipBounds(const TimelineClip& clip, int trackIndex) const
{
    int x = timeToPixel(clip.startTime) + headerWidth;
    int y = 30 + trackIndex * trackHeight + 2; // Account for ruler and padding
    int width = (int)(clip.duration * zoom);
    int height = trackHeight - 4;
    
    return Rectangle<int>(x, y, width, height);
}

Rectangle<int> Timeline::getTrackBounds(int trackIndex) const
{
    return Rectangle<int>(headerWidth, 30 + trackIndex * trackHeight, getWidth() - headerWidth, trackHeight);
}

void Timeline::drawTimeRuler(Graphics& g, Rectangle<int> area)
{
    g.setColour(Colour(0xff3a3a3a));
    g.fillRect(area);
    
    g.setColour(Colours::white);
    g.setFont(12.0f);
    
    // Draw time markers
    double timeStep = 1.0; // 1 second intervals
    if (zoom < 20) timeStep = 4.0; // 4 second intervals for low zoom
    if (zoom > 100) timeStep = 0.5; // 0.5 second intervals for high zoom
    
    for (double time = 0; time < viewStart + (getWidth() / zoom); time += timeStep)
    {
        int x = timeToPixel(time) + headerWidth;
        if (x >= headerWidth && x < getWidth())
        {
            g.drawVerticalLine(x, area.getY(), area.getBottom());
            
            String timeText = String(time, 1) + "s";
            g.drawText(timeText, x + 2, area.getY(), 50, area.getHeight(), Justification::centredLeft);
        }
    }
}

void Timeline::drawTracks(Graphics& g, Rectangle<int> area)
{
    for (int trackIndex = 0; trackIndex < (int)tracks.size(); ++trackIndex)
    {
        auto trackBounds = getTrackBounds(trackIndex);
        trackBounds.setY(trackBounds.getY() - 30); // Adjust for ruler
        
        // Track background
        g.setColour(Colour(0xff2a2a2a));
        g.fillRect(trackBounds);
        
        g.setColour(Colour(0xff404040));
        g.drawRect(trackBounds, 1);
        
        // Draw clips
        auto& track = tracks[trackIndex];
        for (auto& clip : track.clips)
        {
            auto clipBounds = getClipBounds(clip, trackIndex);
            
            // Clip background
            Colour clipColour = clip.colour;
            if (clip.selected)
                clipColour = clipColour.brighter(0.3f);
                
            g.setColour(clipColour);
            g.fillRoundedRectangle(clipBounds.toFloat(), 3.0f);
            
            g.setColour(clipColour.darker(0.3f));
            g.drawRoundedRectangle(clipBounds.toFloat(), 3.0f, 1.0f);
            
            // Clip text
            g.setColour(Colours::white);
            g.setFont(12.0f);
            g.drawText(clip.name, clipBounds.reduced(4), Justification::centredLeft, true);
        }
    }
}

void Timeline::drawPlayhead(Graphics& g, Rectangle<int> area)
{
    int playheadX = timeToPixel(playheadPosition) + headerWidth;
    
    if (playheadX >= headerWidth && playheadX < getWidth())
    {
        g.setColour(Colours::yellow);
        g.drawVerticalLine(playheadX, area.getY(), area.getBottom());
        
        // Playhead handle
        g.fillRect(playheadX - 5, area.getY(), 10, 15);
    }
}
