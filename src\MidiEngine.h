#pragma once

#include <juce_audio_basics/juce_audio_basics.h>
#include <juce_audio_devices/juce_audio_devices.h>
#include <memory>
#include <vector>
#include <functional>

using namespace juce;

//==============================================================================
/**
 * JUCE-based MIDI Engine for DAW Dan
 * Handles MIDI input/output, device detection, and Launchpad integration
 */
class MidiEngine : public MidiInputCallback
{
public:
    //==============================================================================
    MidiEngine();
    ~MidiEngine() override;

    //==============================================================================
    // Device Management
    void refreshMidiDevices();
    StringArray getAvailableInputDevices() const;
    StringArray getAvailableOutputDevices() const;
    
    bool openMidiInput(const String& deviceName);
    bool openMidiOutput(const String& deviceName);
    void closeMidiInput();
    void closeMidiOutput();
    
    bool isMidiInputOpen() const { return midiInput != nullptr; }
    bool isMidiOutputOpen() const { return midiOutput != nullptr; }
    
    //==============================================================================
    // Launchpad Detection
    bool detectLaunchpad();
    bool isLaunchpadConnected() const { return launchpadDetected; }
    String getLaunchpadModel() const { return launchpadModel; }
    
    //==============================================================================
    // MIDI Message Handling
    void handleIncomingMidiMessage(MidiInput* source, const MidiMessage& message) override;
    void sendMidiMessage(const MidiMessage& message);
    
    //==============================================================================
    // Launchpad Control
    void setLaunchpadLED(int x, int y, Colour colour);
    void setLaunchpadLED(int padIndex, Colour colour);
    void clearAllLaunchpadLEDs();
    void setLaunchpadMode(int mode); // 0 = Live mode, 1 = Programmer mode
    
    //==============================================================================
    // Callback Registration
    std::function<void(int, int, float)> onPadPressed;  // x, y, velocity
    std::function<void(int, int)> onPadReleased;        // x, y
    std::function<void(int, float)> onControlChange;    // cc, value
    std::function<void(int, float)> onNoteOn;           // note, velocity
    std::function<void(int)> onNoteOff;                 // note

private:
    //==============================================================================
    // MIDI Devices
    std::unique_ptr<MidiInput> midiInput;
    std::unique_ptr<MidiOutput> midiOutput;
    
    //==============================================================================
    // Launchpad Detection
    bool launchpadDetected = false;
    String launchpadModel;
    int launchpadType = 0; // 0 = Unknown, 1 = Mini, 2 = Pro, 3 = X
    
    //==============================================================================
    // Launchpad MIDI Mapping
    struct LaunchpadMapping
    {
        static constexpr int GRID_START_NOTE = 0x00;
        static constexpr int GRID_END_NOTE = 0x7F;
        static constexpr int TOP_BUTTONS_START = 0x68;
        static constexpr int RIGHT_BUTTONS_START = 0x08;
        
        // Launchpad Pro specific
        static constexpr int PRO_GRID_START = 0x51;
        static constexpr int PRO_GRID_END = 0x58;
    };
    
    //==============================================================================
    // Helper Methods
    void processLaunchpadMessage(const MidiMessage& message);
    void sendLaunchpadSysEx(const Array<uint8>& data);
    Colour juceToPadColour(Colour colour);
    int colourToLaunchpadVelocity(Colour colour);
    
    //==============================================================================
    // Launchpad Models Detection
    bool detectLaunchpadMini();
    bool detectLaunchpadPro();
    bool detectLaunchpadX();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MidiEngine)
};
