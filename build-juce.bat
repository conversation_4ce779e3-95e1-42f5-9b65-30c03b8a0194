@echo off
title DAW Dan - JUCE Build System
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    DAW DAN - JUCE BUILD                     ║
echo  ║                   Modern CMake + JUCE Setup                  ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Add MSYS2 MinGW and CMake paths
set PATH=C:\msys64\mingw64\bin;C:\msys64\usr\bin;%PATH%

REM Check for required tools
echo 🔍 Checking build tools...
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ CMake not found! Please install CMake or check PATH.
    pause
    exit /b 1
)

where g++ >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MinGW g++ not found! Please install MinGW-w64 or check PATH.
    pause
    exit /b 1
)

echo ✅ CMake found: 
cmake --version | findstr "cmake version"
echo ✅ MinGW found:
g++ --version | findstr "g++"

echo.
echo 🏗️  Setting up JUCE build...

REM Clean previous build
if exist build_juce (
    echo 🧹 Cleaning previous build...
    rmdir /s /q build_juce
)
mkdir build_juce
cd build_juce

echo.
echo ⚙️  Configuring CMake with JUCE...
cmake -G "MinGW Makefiles" ^
      -DCMAKE_BUILD_TYPE=Release ^
      -DCMAKE_CXX_COMPILER=g++ ^
      -DCMAKE_C_COMPILER=gcc ^
      ..

if %errorlevel% neq 0 (
    echo ❌ CMake configuration failed!
    echo.
    echo This might be due to:
    echo • Missing JUCE dependencies
    echo • Network issues downloading JUCE
    echo • CMake configuration errors
    echo.
    pause
    exit /b 1
)

echo.
echo 🔨 Building DAW Dan with JUCE...
echo This may take a few minutes for the first build...

mingw32-make -j4

if %errorlevel% neq 0 (
    echo ❌ Build failed!
    echo.
    echo Check the error messages above for details.
    echo Common issues:
    echo • Missing audio drivers
    echo • Compiler errors
    echo • JUCE module issues
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.
echo 📁 Output location: build_juce\bin\DAWDan.exe
echo.

if exist "bin\DAWDan.exe" (
    echo 🎵 DAW Dan is ready to launch!
    echo.
    echo Would you like to run it now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 🚀 Launching DAW Dan...
        start "DAW Dan" "bin\DAWDan.exe"
    )
) else (
    echo ⚠️  Executable not found in expected location.
    echo Check build_juce directory for DAWDan.exe
)

echo.
echo 🎉 JUCE + CMake setup complete!
echo.
pause
