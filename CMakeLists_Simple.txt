# DAW Dan - Simple DirectSound Version
cmake_minimum_required(VERSION 3.15)

project(DAWDanSimple VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Force MinGW-w64 compiler on Windows
if(WIN32)
    set(CMAKE_CXX_COMPILER "g++")
    set(CMAKE_C_COMPILER "gcc")
endif()

# Create console executable (simpler, no GUI issues)
add_executable(DAWDanSimple
    src_simple/main_console.cpp
    src_simple/AudioEngine.cpp
    src_simple/AudioEngine.h
)

# Windows-specific libraries
if(WIN32)
    target_link_libraries(DAWDanSimple
        dsound
        dxguid
        winmm
        user32
        gdi32
        ole32
        comctl32
    )
endif()

# Compiler flags
target_compile_options(DAWDanSimple PRIVATE
    -Wall
    -O2
    -static-libgcc
    -static-libstdc++
)

# Define UNICODE for Windows
target_compile_definitions(DAWDanSimple PRIVATE
    UNICODE
    _UNICODE
)

target_link_options(DAWDanSimple PRIVATE
    -static-libgcc
    -static-libstdc++
    -mconsole
)
