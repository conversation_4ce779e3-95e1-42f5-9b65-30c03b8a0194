# DAW Dan Launcher Script
Write-Host "Setting up DAW Dan..." -ForegroundColor Green

$mingwBin = "C:\msys64\mingw64\bin"
$targetDir = "build_ps\bin"

# Required DLLs for MinGW applications
$requiredDlls = @(
    "libgcc_s_seh-1.dll",
    "libstdc++-6.dll", 
    "libwinpthread-1.dll"
)

# Copy DLLs if they exist
foreach ($dll in $requiredDlls) {
    $sourcePath = Join-Path $mingwBin $dll
    $targetPath = Join-Path $targetDir $dll
    
    if (Test-Path $sourcePath) {
        try {
            Copy-Item $sourcePath $targetPath -Force
            Write-Host "Copied: $dll" -ForegroundColor Yellow
        } catch {
            Write-Host "Warning: Could not copy $dll" -ForegroundColor Red
        }
    } else {
        Write-Host "Warning: $dll not found in MinGW" -ForegroundColor Red
    }
}

Write-Host "Starting DAW Dan..." -ForegroundColor Green

# Run DAW Dan
try {
    Set-Location $targetDir
    & ".\DAW Dan.exe"
} catch {
    Write-Host "Error running DAW Dan: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Press any key to continue..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
